export interface Wallet {
	current_page?: number
	data?: DataWallet[]
	first_page_url?: string
	from?: number
	last_page?: number
	last_page_url?: string
	links?: Links[]
	next_page_url?: string
	path?: string
	per_page?: number
	prev_page_url?: string
	to?: number
	total?: number
}

export interface DataWallet {
	transactionId?: number
	openingBalance?: number
	amount?: number
	closingBalance?: number
	userId?: number
	visitorId?: number
	status?: string
	description?: string
	relatedTransactionId?: number
	createdAt?: string
	updatedAt?: string
	deletedAt?: string
	model_type?: string
	model_id?: number
	checkoutId?: number
}

export interface Links {
	url?: string
	label?: string
	active?: boolean
}
