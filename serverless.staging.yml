# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: actionjo
# "app" enables Serverless Framework Dashboard features and sharing them with other Services.
app: action-v21-frontend-staging-app
service: action-v21-frontend-staging
frameworkVersion: ~4.14.3

provider:
  name: aws
  region: eu-west-1
  runtime: nodejs22.x
  vpc:
    securityGroupIds:
      - sg-0c6b6715fcdd700c5
    subnetIds:
      - subnet-060dd3bb1ec187363 # copilot-v2-prod-priv0
      - subnet-0596eb12436cec3e5 # copilot-v2-prod-priv1
  stage: staging
  # Add API Gateway configuration
  httpApi:
    cors: true
    payload: '2.0'

package:
  patterns:
    - '!**'
    - '.output/**'

functions:
  app:
    handler: .output/server/index.handler
    url:
      cors:
        allowCredentials: false
    events:
      - httpApi: '*' # Catch all routes and forward to the Nuxt app
    timeout: 30
    memorySize: 1024
    environment:
      BASE_URL: ${env:STAGING_BASE_URL}
      APP_URL: ${env:STAGING_APP_URL}
      ENVIRONMENT: ${env:NODE_ENV}
      HYPERPAY_URL: ${env:HYPERPAY_URL}
      ENABLE_CACHE: ${env:ENABLE_CACHE}
      DEBUG_CACHE: ${env:DEBUG_CACHE}
      CACHE_TOKEN: ${env:STAGING_CACHE_TOKEN}
      REDIS_HOST: ${env:STAGING_REDIS_HOST}
      REDIS_PORT: ${env:STAGING_REDIS_PORT}
      REDIS_DB: ${env:STAGING_REDIS_DB}
      SENTRY_DSN: ${env:SENTRY_DSN}
      GTM_ID: ${env:GTM_ID}
