import * as Sentry from '@sentry/nuxt'

const config = useRuntimeConfig()

Sentry.init({
	// If set up, you can use your runtime config here
	// dsn: useRuntimeConfig().public.sentry.dsn,
	dsn: config.public.sentry.dsn,

	// Setting this option to true will print useful information to the console while you're setting up Sentry.
	// debug: true,
	environment: config.public.sentry.environment,
	enabled: true,
	// tracesSampleRate: 1.0,
})
