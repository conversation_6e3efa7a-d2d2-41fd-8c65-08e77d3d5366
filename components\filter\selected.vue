<script setup lang="ts">
import { Badge } from '@/components/ui/badge/index'
import { useCurrency } from '~/composables/useCurrency.ts'

const emit = defineEmits<{
	clear: [value: string]
}>()
const { priceFormat } = useCurrency()

interface Props {
	filter?: object
	loading?: boolean
}

const { filter, loading } = defineProps<Props>()
const route = useRoute()
const router = useRouter()
const routeQuery = computed(() => route.query)

/** Prepare ram filter **/
const rams = computed<string[]>(() => {
	if (!routeQuery?.value || !routeQuery.value?.['ram'] || !filter?.ram) {
		return []
	}

	return (routeQuery?.value?.ram as string)
		?.split(',')
		?.map(r => ({
			...filter.ram.options.find(i => i?.value == r),
			type: 'ram',
		})).map((item) => {
			return {
				...item,
				text: `${item.type}  (${item.text} ${filter?.ram?.config?.suffix})`,
			}
		})
})

/** Prepare storage filter **/
const storage = computed<string[]>(() => {
	if (!routeQuery?.value || !routeQuery.value?.['storage'] || !filter?.storage) {
		return []
	}

	return (routeQuery?.value?.storage as string)
		?.split(',')
		?.map(r => ({
			...filter.storage.options.find(i => i?.value == r),
			type: 'storage',
		})).map((item) => {
			return {
				...item,
				text: `${filter?.storage?.name || item.type} (${item.text} ${filter?.storage?.config?.suffix}) `,
			}
		})
})

/** Prepare brand filter **/
const brandId = computed<string[]>(() => {
	if (!routeQuery?.value || !routeQuery.value?.['brandId'] || !filter?.brandId) {
		return []
	}

	return (routeQuery?.value?.brandId as string)
		?.split(',')
		?.map(r => ({
			...filter.brandId.options.find(i => i?.value == r),
			type: 'brand',
		}))
})

/** Prepare price filter **/
const price = computed(() => {
	if (!routeQuery.value?.price || !filter?.price) {
		return []
	}

	return [
		{
			text: (routeQuery?.value?.price as string)
				.split(':')
				.map(p => priceFormat(Number(p)))
				.join(' - ') as string,
			type: 'price',
		},
	]
})

/**
 * List all selected filters
 */
const list = computed<string | unknown[] | unknown>(() => {
	return [
		...price.value,
		...rams.value,
		...storage.value,
		...brandId.value,
	]
})

/**
 * Remove selected filter and update the page query
 * @param selected
 */
interface SelectedType {
	type?: string
	value?: string | string[] | unknown
}

const onRemoveFilter = (selected: SelectedType): void => {
	const newQuery = { ...route.query }

	const paramValues = newQuery[selected.type] as string

	if (selected.type == 'price') {
		delete newQuery[selected.type]
	} else if (paramValues) {
		const newValues = paramValues
			.split(',')
			.filter(val => val !== String(selected.value))
			.join(',')

		if (newValues) {
			newQuery[selected.type] = newValues
		} else {
			delete newQuery[selected.type]
		}
	}

	router.push({ path: route.path, query: newQuery })
}
</script>

<template>
	<CardHeader class="flex flex-row w-full justify-between items-center py-4 ps-6 border-b mb-2">
		<div
			v-if="loading"
			class="flex justify-between items-center w-full"
		>
			<Skeleton class="h-8 w-1/3 border-gray-200" />
			<Skeleton class="h-8 w-1/3 border-gray-200" />
		</div>
		<template v-else>
			<span class="text-sm text-gray-600">
				{{ $t('filters.selected-title') }}
				<span v-if="!!list?.length">
					({{ list?.length }})
				</span>
			</span>
			<button
				class="text-sm text-primary-600 hover:opacity-50"
				@click="emit('clear')"
			>
				{{ $t('filters.clear-title') }}
			</button>
		</template>
	</CardHeader>

	<div
		v-if="list.length"
		class="flex max-w-full flex-wrap gap-2 border-b border-gray-200 pb-4 mb-2 mx-4"
	>
		<template v-if="loading">
			<Skeleton
				v-for="(_, index) in Array(5)"
				:key="`loading-${index}`"
				class="h-5 w-1/4 bg-sky-50"
			/>
		</template>
		<div
			v-for="(item, index) in list"
			v-else
			:key="index"
		>
			<Badge variant="selected">
				<div
					class="inline-flex gap-2 justify-center items-center text-nowrap cursor-pointer"
					@click="onRemoveFilter(item)"
				>
					<span>{{ item?.text || item }}</span>
					<Icon
						name="lucide:circle-x"
						size="12px"
					/>
				</div>
			</Badge>
		</div>
	</div>
</template>
