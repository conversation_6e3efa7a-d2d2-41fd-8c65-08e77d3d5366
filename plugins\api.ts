/**
 * Enhanced API Plugin with Sentry Integration
 *
 * Features:
 * - Automatic payload logging for POST/PUT/PATCH requests
 * - Enhanced error context for Sentry debugging
 * - Sensitive data sanitization (passwords, tokens, etc.)
 * - Specific handling for validation errors (422)
 * - Request/response correlation for better debugging
 * - Automatic cleanup of stored request contexts
 */

// import type { Fetch } from 'ofetch'
import { toast } from 'vue-sonner'
import * as Sentry from '@sentry/nuxt'
import { useUserToken } from '~/composables/useUserToken'
import { useAuthStore } from '~/store/useAuthStore.client'

interface ApiResponse {
	status?: number
	message?: string | string[]
	_data?: { message?: string | string[] }
}

interface RequestContext {
	method: string
	url: string
	payload?: unknown
	headers?: Record<string, string>
	timestamp: number
}

/**
 * Safely serialize data for Sentry logging
 */
function safeSerialize(data: unknown): unknown {
	try {
		return JSON.parse(JSON.stringify(data))
	} catch {
		return { message: 'Unable to serialize data due to circular references or other issues' }
	}
}

/**
 * Sanitize sensitive data from payloads before logging
 */
function sanitizePayload(payload: unknown): unknown {
	if (!payload || typeof payload !== 'object') {
		return payload
	}

	const sensitiveFields = ['password', 'confirmPassword', 'token', 'authorization', 'apiKey', 'secret']
	const sanitized = { ...payload as Record<string, unknown> }

	for (const field of sensitiveFields) {
		if (field in sanitized) {
			sanitized[field] = '[REDACTED]'
		}
	}

	return sanitized
}

/**
 * Add API request context to Sentry
 */
function addApiContextToSentry(context: RequestContext, response?: ApiResponse) {
	Sentry.withScope((scope) => {
		scope.setTag('api_call', true)
		scope.setTag('api_method', context.method)
		scope.setTag('api_url', context.url)

		scope.setContext('api_request', {
			method: context.method,
			url: context.url,
			timestamp: context.timestamp,
			headers: context.headers ? safeSerialize(context.headers) : undefined,
			payload: context.payload ? sanitizePayload(safeSerialize(context.payload)) : undefined,
		})

		if (response) {
			scope.setContext('api_response', {
				status: response.status,
				message: response.message,
				data: response._data ? safeSerialize(response._data) : undefined,
			})
		}
	})
}

export default defineNuxtPlugin({
	name: 'api',
	setup(nuxtApp) {
		const config = useRuntimeConfig()
		const localePath = useLocalePath()
		const { visitorId } = useVisitorId()
		const $i18n = nuxtApp.$i18n

		// Store for request contexts
		const requestContextMap = new Map<string, RequestContext>()

		const $api = $fetch.create({
			baseURL: config.public.baseUrl as string,
			timeout: 20000,
			mode: 'cors',
			onRequest({ options, request }) {
				const { userToken } = useUserToken()

				if ($i18n['locale']) {
					options.headers.set('Language', $i18n['locale']?.value)
				}

				if (visitorId.value) {
					options.headers.set('visitor-id', visitorId.value)
				}

				if (userToken.value) {
					options.headers.set('authorization', `Bearer ${userToken.value}`)
				} else if (request.toString().includes('/my/')) {
					throw new Error('Unauthorized request blocked: /my/ route requires token.')
				}

				// Store request context for potential error logging
				const method = options.method?.toLowerCase() || 'get'
				if (['post', 'put', 'patch', 'delete'].includes(method)) {
					const requestKey = `${method}_${request.toString()}_${Date.now()}`
					const requestContext: RequestContext = {
						method: method.toUpperCase(),
						url: request.toString(),
						payload: options.body,
						headers: Object.fromEntries(options.headers.entries()),
						timestamp: Date.now(),
					}

					// Store context for later use in error handler
					requestContextMap.set(requestKey, requestContext)

					// Clean up old entries (keep only last 10)
					if (requestContextMap.size > 10) {
						const oldestKey = requestContextMap.keys().next().value
						requestContextMap.delete(oldestKey)
					}
				}
			},
			async onResponseError({ response, request, options }) {
				const { status, message, _data } = response as ApiResponse
				const authStore = useAuthStore()

				// Find matching request context for enhanced logging
				const requestUrl = request.toString()
				const method = options.method?.toLowerCase() || 'get'
				let requestContext: RequestContext | undefined

				// Find the most recent matching request context
				for (const [key, context] of requestContextMap.entries()) {
					if (context.url === requestUrl && context.method.toLowerCase() === method) {
						requestContext = context
						requestContextMap.delete(key) // Clean up after use
						break
					}
				}

				// Add enhanced context to Sentry for API errors
				if (['post', 'put', 'patch', 'delete'].includes(method) || status >= 400) {
					addApiContextToSentry(
						requestContext || {
							method: method.toUpperCase(),
							url: requestUrl,
							timestamp: Date.now(),
							headers: Object.fromEntries(options.headers?.entries() || []),
						},
						{ status, message, _data },
					)

					// Capture the API error to Sentry with detailed context
					Sentry.captureException(new Error(`API ${status} Error: ${requestUrl}`), {
						tags: {
							api_error: true,
							api_status: status,
							api_method: method.toUpperCase(),
						},
						level: status >= 500 ? 'error' : 'warning',
					})
				}

				switch (status) {
					case 401:
						if (authStore.isLoggedIn) {
							authStore.removeUserSession()
							// toast.error($i18n['t']('error.unauthorized'))
						}
						navigateTo(localePath({ path: '/' }))
						break
					case 406:
						toast.error('Something went wrong, 406')
						navigateTo(localePath({ name: 'home' }))
						break
					case 403:
						toast.error('Something went wrong, 403')
						throw createError({
							statusCode: 403,
						})
					case 422:
						// Enhanced logging for validation errors
						console.log('Validation Error Details:', {
							url: requestUrl,
							payload: requestContext?.payload ? sanitizePayload(requestContext.payload) : undefined,
							response: _data,
							timestamp: new Date().toISOString(),
						})

						// Add specific context for validation errors
						Sentry.withScope((scope) => {
							scope.setTag('validation_error', true)
							scope.setLevel('warning')
							scope.setContext('validation_details', {
								endpoint: requestUrl,
								method: method.toUpperCase(),
								submitted_data: requestContext?.payload ? sanitizePayload(safeSerialize(requestContext.payload)) : undefined,
								validation_errors: _data ? safeSerialize(_data) : undefined,
							})
							Sentry.captureMessage(`Validation Error: ${requestUrl}`)
						})

						toast.error(_data?.message || message || 'Validation error occurred')
						break
					default:
						console.log('API Error:', requestUrl, status, _data?.message || message)

						// Log additional context for debugging
						if (requestContext) {
							console.log('Request Context:', {
								method: requestContext.method,
								payload: sanitizePayload(requestContext.payload),
								timestamp: new Date(requestContext.timestamp).toISOString(),
							})
						}

						toast.error(_data?.message || message || 'Something went wrong')
				}
			},
			async onResponse({ response, request, options }) {
				const method = options.method?.toLowerCase() || 'get'

				// Log successful API calls for POST/PUT/PATCH for debugging
				if (['post', 'put', 'patch'].includes(method) && response.status < 400) {
					console.log(`API ${method.toUpperCase()} Success:`, {
						url: request.toString(),
						status: response.status,
						timestamp: new Date().toISOString(),
					})

					// Clean up any stored context for this request
					const requestUrl = request.toString()
					for (const [key, context] of requestContextMap.entries()) {
						if (context.url === requestUrl && context.method.toLowerCase() === method) {
							requestContextMap.delete(key)
							break
						}
					}
				}
			},
		})

		return {
			provide: {
				api: $api,
			},
		}
	},
})
