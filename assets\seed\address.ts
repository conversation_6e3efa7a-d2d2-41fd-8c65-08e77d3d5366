import type { Address } from '~/interfaces/auth/address'

export const addressDefaultValues: Address = {
	addressId: 0,
	cityId: 0,
	district: '',
	userId: 0,
	phone: {
		iso: 'JO',
		code: '962',
		number: '',
	},
	street: '',
	recipientName: '',
	apartmentNumber: '',
	buildingNumber: '',
	default: 0,
	visitorId: null,
	firstName: '',
	lastName: '',
	email: '',
	buildingType: 'home',
	city: {
		cityId: 0,
		name: '',
		countryId: 0,
		codeCity: '',
		country: {
			countryId: 0,
			name: '',
			mediaId: null,
			ISOCode: 'JO',
			phoneCode: '962',
		},
	},
}
