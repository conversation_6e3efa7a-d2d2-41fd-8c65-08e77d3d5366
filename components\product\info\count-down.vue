<script setup lang="ts">
import type { Stock } from '~/interfaces/product/details'

const { stock } = defineProps<{
	stock?: Stock
}>()

const offerTimer = useOfferTime(new Date(stock?.unPublishedAt))
</script>

<template>
	<div class="flex items-center py-4 justify-center tabular-nums">
		<b class="text-sm">{{ $t('product.offer-end-title') }}</b>
		<span class="text-sm ps-2 tabular-nums">{{ offerTimer }}</span>
	</div>
</template>

<style scoped lang="scss">

</style>
