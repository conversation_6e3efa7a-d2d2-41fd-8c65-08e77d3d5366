<script setup lang="ts">
import type { WithClassAsProps } from './interface'
import { useCarousel } from './useCarousel'
import { cn } from '@/lib/utils'

defineOptions({
	inheritAttrs: false,
})

const props = defineProps<WithClassAsProps>() as WithClassAsProps

const { carouselRef, orientation } = useCarousel()
</script>

<template>
	<div
		ref="carouselRef"
		class="overflow-hidden"
	>
		<div
			:class="
				cn(
					'flex',
					orientation === 'horizontal' ? '' : '-mt-4 flex-col',
					props.class,
				)"
			v-bind="$attrs"
		>
			<slot />
		</div>
	</div>
</template>
