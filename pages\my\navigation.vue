<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import type { User } from '~/interfaces/auth/auth'
import { useAuthStore } from '~/store/useAuthStore.client'

const isDesktop = useMediaQuery('(min-width: 600px)')
const authStore = useAuthStore()
const userData = ref(null)

definePageMeta({
	ssr: false,
	middleware: 'auth',
	name: 'navigation',
})

/** switch to desktop view **/
const switchScreen = useDebounceFn(() => {
	if (isDesktop.value) {
		const localePath = useLocalePath()
		navigateTo(localePath('/my/profile'))
	}
}, 500)

watch(() => authStore.user, (user) => {
	userData.value = user as User
}, { immediate: true, deep: true })

watch(() => isDesktop, () => {
	switchScreen()
}, { immediate: true, deep: true })
</script>

<template>
	<div
		v-if="userData"
		class="flex flex-col w-full"
	>
		<ProfileNavigation :user="userData" />
	</div>
</template>
