name: Deploy Staging with Serverless
run-name: ${{ github.actor }} trigger staging deployment 🚀 🟧

on:
   push:
      branches:
         - stg

jobs:
   deploy:
      runs-on: ubuntu-latest
      environment: staging  # This gives access to "staging" environment secrets

      steps:
         -  name: Checkout repository
            uses: actions/checkout@v4

         -  name: Setup Node.js
            uses: actions/setup-node@v4
            with:
               node-version: '22'  # or your project's version

         -  name: Install dependencies
            run: npm ci
            env:
               APP_URL: ${{ vars.STAGING_APP_URL }}
               BASE_URL: ${{ vars.STAGING_BASE_URL }}

         -  name: Set Node.js memory limit
            run: export NODE_OPTIONS="--max-old-space-size=4096"

         -  name: build
            run: npm run build:lambda
            env:
               NODE_OPTIONS: "--max-old-space-size=4096"
               APP_URL: ${{ vars.STAGING_APP_URL }}
               BASE_URL: ${{ vars.STAGING_BASE_URL }}
               ENVIRONMENT: ${{ vars.ENVIRONMENT }}
               REDIS_HOST: ${{ vars.STAGING_REDIS_HOST }}
               REDIS_PORT: ${{ vars.STAGING_REDIS_PORT }}
               REDIS_DB: ${{ vars.STAGING_REDIS_DB }}
               SENTRY_DSN: ${{ vars.SENTRY_DSN }}
               SENTRY_ORG: ${{ vars.SENTRY_ORG }}
               SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
               SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
               GTM_ID: ${{vars.GTM_ID || ''}}
         -  name: Configure AWS credentials
            uses: aws-actions/configure-aws-credentials@v2
            with:
               aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
               aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               aws-region: eu-west-1

         -  name: Deploy Staging with Serverless
            env:
               SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
               # or if using AWS credentials directly
               AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
               AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               # Environment variables for serverless.staging.yml
               STAGING_BASE_URL: ${{ vars.STAGING_BASE_URL }}
               STAGING_APP_URL: ${{ vars.STAGING_APP_URL }}
               NODE_ENV: staging
               DEBOUNCE_TIME: ${{ vars.DEBOUNCE_TIME || '2001' }}
               HYPERPAY_URL: ${{ vars.HYPERPAY_URL || 'https://test.oppwa.com' }}
               ENABLE_CACHE: ${{ vars.ENABLE_CACHE || 'true' }}
               DEBUG_CACHE: ${{ vars.DEBUG_CACHE || 'true' }}
               STAGING_CACHE_TOKEN: ${{ secrets.STAGING_CACHE_TOKEN }}
               STAGING_REDIS_HOST: ${{ vars.STAGING_REDIS_HOST }}
               STAGING_REDIS_PORT: ${{ vars.STAGING_REDIS_PORT }}
               STAGING_REDIS_DB: ${{ vars.STAGING_REDIS_DB }}
               SENTRY_DSN: ${{ vars.SENTRY_DSN }}
               SENTRY_ENVIRONMENT: staging
               GTM_ID: ${{vars.GTM_ID || ''}}
            run: npm run deploy:staging

         -  name: Clear cache after deployment
            run: |
               curl --location '${{ vars.STAGING_APP_URL }}/api/cache/clear' \
               --request POST \
               --header 'Content-Type: application/json' 
