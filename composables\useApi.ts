import { useFetch, useNuxtApp, type UseFetchOptions } from 'nuxt/app'
import type { FetchOptions } from 'ofetch'

export function useApi<T = unknown, B = object>(
	url: string | (() => string),
	options: (FetchOptions & { body?: B }) | UseFetchOptions<T> = {},
) {
	try {
		const { $api } = useNuxtApp()

		return useFetch(url, {
			lazy: true,
			...options as object,
			$fetch: $api as typeof $fetch,
		})
	} catch (e) {
		throw e?.toJSON() ?? e
	}
}
