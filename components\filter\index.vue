<script setup lang="ts">
// @ts-nocheck
import PriceRange from './price-range.vue'
import Options from '~/components/filter/options.vue'

interface Props {
	filter?: object | string | unknown
	loading?: boolean
	update?: Function
}
const { filter, loading, update } = defineProps<Props>()

const router = useRouter()
const route = useRoute()
const key = ref(1)

interface DefaultSelect {
	ram?: string[]
	price?: string
	storage?: string[]
	brandId?: string[]
}

/**
 * Set default selected Ids and filters
 */
const defaultSelect = computed(() => {
	const result = {}

	for (const key in route.query) {
		if (key === 'price') {
			if (!result[key]) {
				result[key] = (route.query[key] as string).split(':').map(Number)
			}
		} else {
			result[key] = (route.query[key] as string)
				.split(',')
				.map(value => Number(value))
		}
	}

	return result as DefaultSelect
})

/** Remove all filters in additional the page number and reset components */
const onClearFilter = async () => {
	await router.push({ path: route.path, query: undefined, force: true })
	return nextTick(() => {
		key.value += 1
	})
}
</script>

<template>
	<Card class="flex w-full flex-col gap-2 py-0 max-sm:overflow-y-auto">
		<filter-selected
			:filter="filter"
			:loading="loading"
			@clear="onClearFilter"
		/>
		<CardContent
			:key="`filter-${key}`"
			class="flex-col w-full"
		>
			<template v-if="loading">
				<div class="flex flex-col w-full gap-3">
					<Skeleton class="h-5 w-full border-gray-300" />
					<Skeleton class="h-5 w-full border-primary-300" />
					<Skeleton class="h-5 w-full border-gray-400" />
					<Skeleton class="h-5 w-full border-gray-400" />
					<Skeleton class="h-5 w-full border-gray-400" />
					<Skeleton class="h-5 w-full border-gray-400" />
				</div>
			</template>

			<div
				v-else
				class="flex flex-col w-full"
			>
				<template
					v-if="filter?.price"
				>
					<PriceRange
						:price="filter.price"
						:update="(price) => update({ price })"
					/>
				</template>

				<template
					v-if="filter?.ram"
				>
					<Options
						:filter="filter.ram"
						:selected="defaultSelect?.ram"
						:update="(value) => update({ ram: value })"
					/>
				</template>

				<template
					v-if="filter?.storage"
				>
					<Options
						:filter="filter.storage"
						:selected="defaultSelect?.storage"
						:update="(value) => update({ storage: value })"
					/>
				</template>

				<template
					v-if="!!filter?.brandId"
				>
					<Options
						:filter="filter.brandId"
						:selected="defaultSelect?.brandId"
						:update="(value) => update({ brandId: value })"
					/>
				</template>
			</div>
		</CardContent>
	</Card>
</template>
