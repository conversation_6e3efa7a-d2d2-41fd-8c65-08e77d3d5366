import { defineStore } from 'pinia'
import type { Categories } from '~/interfaces/category/categories'

export const useCategoriesStore = defineStore('categories', {
	state: () => ({
		categories: [] as Categories[],
		fetching: true as boolean,
		error: null,
	}),
	actions: {
		async fetch() {
			try {
				this.fetching = true
				const { data, error, status } = await useApi<Categories[]>('categories')
				this.fetching = false
				if (error.value) throw error.value

				if (status.value === 'success') {
					this.setCategories(data.value as Categories[])
				}
			} catch (error) {
				console.log('Error on fetching categories', error.value)
			}
		},

		async fetchMounted() {
			this.fetching = true
			const { $api } = useNuxtApp()

			return $api<Categories[]>('/categories')
				.then(async (data) => {
					this.setCategories(data)
				})
				.catch((err) => {
					console.error('Error fetching categories:', err)
				})
				.finally(() => {
					this.fetching = false
				})
		},

		setCategories(categories: Categories[]) {
			this.categories = categories
		},
	},

	getters: {
		search: ({ categories }) => {
			return categories?.map((category: Categories) => ({
				id: category.categoryId,
				name: category.name,
				value: category.value,
				slug: category.slug,
				media: category?.cover?.src,
			}))
		},

		links: ({ categories }) => {
			return categories?.map((category: Categories) => ({
				id: category.categoryId,
				name: category.name,
				value: category.value,
				slug: category.slug,
				children: category?.children?.map((child: Categories) => ({
					name: child.name,
					value: child.value,
					slug: child.slug,
				})),
			}))
		},

		slider: ({ categories }) => {
			return categories?.map((category: Categories) => ({
				id: category.categoryId,
				name: category.name,
				value: category.value,
				slug: category.slug,
				media: category?.cover?.src,
			}))
		},
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useCategoriesStore, import.meta.hot))
}
