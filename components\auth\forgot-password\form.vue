<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type FormPhoneValue from '~/interfaces/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const { t } = useI18n()

const authStore = useAuthStore()
const isLoading = ref(false)
const emit = defineEmits<{
	(event: 'set:step', value: number): void
}>()

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			phone: z.object({
				number: z.string().optional(),
				iso: z.string().optional(),
				code: z.string().optional(),
				isValid: z.boolean().optional(),
			}).refine(phone => phone.isValid, t('error.phone-number-invalid')),
		}),
	),

	initialValues: toRaw({
		phone: {
			code: '962',
			iso: 'JO',
			number: '',
			isValid: true,
		},
	}),
})

/** Handle on login user **/
const forgotPassword = Form.handleSubmit(async (values) => {
	isLoading.value = true
	return authStore.forgotPasswordPhone(values.phone)
		.then((data) => {
			authStore.forgotForm = {
				userData: data,
				phone: { ...values.phone },
			}
			toast.success(t('form.verification-code-sent'))
			emit('set:step', 2)
		})
		.catch((error) => {
			throw error
		})
		.finally(() => {
			nextTick(() => isLoading.value = false)
		})
})

const onUpdate = (value: FormPhoneValue): void => {
	Form.setFieldValue('phone', {
		number: value.nationalNumber,
		code: value.countryCallingCode,
		iso: value.countryCode,
		isValid: value.isValid,
	})
}

defineExpose({
	submitForm: forgotPassword,
	isLoading,
})
</script>

<template>
	<div class="flex flex-col gap-4 pt-6 min-h-32">
		<FormPhone
			:error="Form.errors?.value?.phone"
			@update="onUpdate"
		/>
	</div>
</template>
