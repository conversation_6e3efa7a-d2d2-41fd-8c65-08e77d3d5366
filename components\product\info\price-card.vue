<script setup lang="ts">
import type { UseProductDetails } from '~/interfaces/product/details'

const { product, loading } = defineProps<{
	product?: UseProductDetails
	loading?: boolean
}>()
const {
	hasStock,
	hasOffer,
	discountPercent,
	discountAmount,
	priceFormatted,
	offerPriceFormatted,
	discountAmountFormatted,
	hasDiscount,
} = product as UseProductDetails
</script>

<template>
	<!-- header -->
	<Skeleton
		v-if="loading"
		class="w-full h-full min-h-24 bg-sky-50 rounded "
	/>
	<div
		v-else
		class="flex bg-sky-50 flex-col p-4 rounded"
	>
		<template v-if="!hasStock">
			<div class="flex w-full flex-col gap-2">
				<div class="flex w-full justify-between items-center">
					<div class="text-2xl font-bold">
						{{ $t('product.out-stock') }}
					</div>

					<Icon
						name="ui:out-stock"
						size="25px"
					/>
				</div>

				<div class="flex items-center gap-2">
					<Icon name="ui:bell-ringing" />
					<span class="text-sm font-normal">
						{{ $t('product.let-me-know-when-available') }}
					</span>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="flex items-start justify-between">
				<div class="flex flex-col">
					<span class="text-3xl text-primary-600 font-bold">{{ priceFormatted }}</span>
					<div
						v-if="discountAmount"
						class="flex gap-2 items-center text-md"
					>
						<span class="text-gray-600 line-through">{{ offerPriceFormatted }}</span>
						<span class="text-orange-400 font-medium">
							{{ $t('product.discount-amount-title', { amount: discountAmountFormatted }) }}
						</span>
					</div>
				</div>

				<div
					v-if="hasDiscount"
					class="flex"
				>
					<span class="bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full">
						{{ $t('product.card-discount', { amount: discountPercent }) }}
					</span>
				</div>
			</div>
			<ClientOnly>
				<ProductInfoCountDown
					v-if="hasOffer"
					:stock="product?.variance?.stock"
				/>
			</ClientOnly>
		</template>
	</div>
	<!-- /header -->
</template>

<style scoped lang="scss">

</style>
