<script setup lang="ts">
import { toast } from 'vue-sonner'
import type { Address } from '~/interfaces/auth/address'

const { address, loading = false } = defineProps<{
	address?: Address
	loading?: boolean
}>()

const emit = defineEmits<{
	(event: 'fetch:address'): void
	(event: 'delete:address', value: number): void
	(event: 'edit:address', value: number): void
}>()

const { t } = useI18n()
const isDefault = ref<boolean>(address?.default === 1)

/** Address Details **/
const addressDetails = computed(() => {
	return [
		address?.city?.country?.name || '',
		address?.city?.name || '',
		address?.district || '',
		address?.street || '',
		address?.apartmentNumber || '',
		address?.buildingNumber || '',
	].join(', ')
})

const phoneNumber = computed(() => {
	return [
		'+',
		address?.phone?.code + ' ',
		address?.phone?.number,
	].join('')
})

const toggleDefault = async (): Promise<void> => {
	const { $api } = useNuxtApp()
	return $api<unknown>(`my/addresses/set-default-address/${address.addressId}`, {
		method: 'PUT',
		body: {
			...address,
			default: 1,
		},
	})
		.then(() => {
			toast.success(t('form.address-default-updated'))
			emit('fetch:address')
		})
		.catch((error) => {
			console.log('toggle default error', error)
		})
}

watch(() => address, () => {
	isDefault.value = address?.default === 1
}, { immediate: true, deep: true })
</script>

<template>
	<div class="flex rounded-lg border border-gray-200 p-4 gap-4 w-full">
		<template v-if="loading">
			<div class="w-full flex gap-2 flex-col">
				<div class="flex gap-2 justify-between items-center">
					<Skeleton class="w-1/3 h-5" />
					<Skeleton class="w-1/3 h-5" />
				</div>
				<Skeleton class="w-1/2 h-5" />
				<div class="flex gap-2 justify-between items-center">
					<Skeleton class="w-1/3 h-5" />
					<Skeleton class="w-1/3 h-5" />
				</div>
			</div>
		</template>
		<template v-else>
			<div class="flex flex-col flex-grow gap-4 max-sm:text-sm">
				<div class="flex justify-between items-center w-full">
					<div class="flex w-1/3 font-bold">
						{{ $t(`form.${address.buildingType}`) }}
					</div>
					<div class="flex justify-end items-center gap-2 w-2/3">
						<label
							:for="`set-default-${address.addressId}`"
						>{{ $t('form.set-default-address') }}</label>
						<Switch
							:id="`set-default-${address.addressId}`"
							v-model="isDefault"
							:disabled="isDefault"
							:class="{ 'opacity-50 pointer-event-none': isDefault }"
							@update:model-value.once="toggleDefault"
						/>
					</div>
				</div>
				<div class="flex justify-between items-center gap-4 w-full">
					<div class="flex w-full items-center gap-4">
						<div class="w-24 font-semibold">
							{{ $t('form.receiver-name') }}:
						</div>
						<span>
							{{ address.recipientName }}
						</span>
					</div>
				</div>

				<div class="flex w-full items-center gap-4">
					<div class="w-24 font-semibold">
						{{ $t('form.address') }}:
					</div>
					<span>
						{{ addressDetails }}
					</span>
				</div>

				<div class="flex justify-between items-center gap-4 w-full">
					<div class="flex w-full items-center gap-4">
						<div class="w-24 font-semibold">
							{{ $t('form.phone') }}:
						</div>
						<span
							dir="ltr"
							class="text-nowrap"
						>{{ phoneNumber }}</span>
					</div>
					<div class="flex items-center gap-2 w-full justify-end">
						<button
							class="flex p-1 hover:bg-gray-100 rounded-lg text-gray-600"
							@click.prevent="() => emit('edit:address', address.addressId)"
						>
							<Icon name="lucide:pencil" />
						</button>

						<button
							class="flex p-1 hover:bg-gray-100 rounded-lg text-gray-600"
							@click="() => emit('delete:address', address.addressId)"
						>
							<Icon name="lucide:trash-2" />
						</button>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<style scoped lang="scss">

</style>
