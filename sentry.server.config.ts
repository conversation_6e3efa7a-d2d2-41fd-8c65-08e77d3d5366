import * as Sentry from '@sentry/nuxt'
import { env } from '@/env.config'

Sentry.init({
	// dsn: useRuntimeConfig().public.sentry.dsn,
	dsn: env.SENTRY_DSN,

	// Setting this option to true will print useful information to the console while you're setting up Sentry.
	// debug: true,
	enabled: true,
	environment: env.ENVIRONMENT,
	// tracesSampleRate: 1.0,
	// environment: useRuntimeConfig().public.sentry.environment,
	// environment: 'staging',
})
