<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { Address, CityResponse } from '~/interfaces/auth/address'
import type FormPhoneValue from '~/interfaces/form'

const emit = defineEmits<{
	(event: 'fetch:address', isSuccessAdd?: boolean): void
	(event: 'close:address'): void
}>()

const { address = null } = defineProps<{
	address?: Address | null
}>()

const { t } = useI18n()
const { data } = await useApi<CityResponse[]>('lookups-website/cities')
const cites = computed(() => data.value as CityResponse[])

const form = ref({ ...address })
const isDisabledDefault = computed<boolean>(() => address?.default === 1)
const loading = ref<boolean>(false)
const isEdit = computed<boolean>(() => !!address?.addressId)

const isDefault = computed<boolean | undefined>({
	get: () => !!Form.values.default as boolean,
	set: (val: boolean | number) => {
		Form.setFieldValue('default', !!val)
	},
})

const isProfileDataShared = computed<boolean | undefined>({
	get: () => !!Form.values.isProfileDataShared as boolean,
	set: (val: boolean) => {
		Form.setFieldValue('isProfileDataShared', !!val)
	},
})

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			cityId: z.number()
				.min(1, t('error.required'))
				.nullish()
				.refine(val => val !== null && val !== undefined, {
					message: t('error.required'),
				}),
			district: z.string().min(1, t('error.required')),
			fullAddress: z.string().optional(),
			buildingType: z.string().min(1, t('error.required')),
			default: z.union([z.boolean(), z.number()]).optional(),
			isProfileDataShared: z.union([z.boolean(), z.number()]).optional(),
			firstName: z.string().nullable().optional(),
			lastName: z.string().nullable().optional(),
			phone: z.object({
				number: z.string().nullable().optional(),
				iso: z.string().nullable().optional(),
				code: z.string().nullable().optional(),
				isValid: z.boolean().optional(),
			}).nullable().optional(),
		}).superRefine((data, ctx) => {
			if (!data.isProfileDataShared) {
				if (!data.firstName || data.firstName.trim() === '') {
					ctx.addIssue({
						path: ['firstName'],
						code: z.ZodIssueCode.custom,
						message: t('error.required'),
					})
				}
				if (!data.lastName || data.lastName.trim() === '') {
					ctx.addIssue({
						path: ['lastName'],
						code: z.ZodIssueCode.custom,
						message: t('error.required'),
					})
				}
				if (!data.phone?.isValid) {
					ctx.addIssue({
						path: ['phone'],
						code: z.ZodIssueCode.custom,
						message: t('error.phone-number-invalid'),
					})
				}
				return ctx
			} else {
				data.firstName = undefined
				data.lastName = undefined
				data.phone = undefined
				return data
			}
		}),
	),
	initialValues: toRaw({
		...form.value,
	}),
	validateOnMount: false,
})

/** update address value */
const updateAddress = async (data: Address) => {
	const payload = {
		...data,
	}
	const { $api } = useNuxtApp()
	return $api<unknown>(`addresses/${address.addressId}`, {
		method: 'PUT',
		body: payload,
	})
}

/** create address value */
const createAddress = async (payload: Address) => {
	const { $api } = useNuxtApp()
	await $api(`addresses`, {
		method: 'POST',
		body: payload,
	})
}

/** handle on submit the form **/
const onSubmitChange = Form.handleSubmit(async (payload: Address): Promise<void> => {
	loading.value = true

	let request = null
	if (isEdit.value) {
		request = updateAddress(payload)
	} else {
		request = createAddress(payload)
	}

	return request
		.then(() => {
			toast.success(t('form.address-added-success'))
			emit('fetch:address', !isEdit.value)
		})
		.finally(() => {
			Form.resetForm()
			nextTick(() => loading.value = false)
		})
})
</script>

<template>
	<Modal
		:title="$t('form.address-modal-title')"
		:size="'max-w-xl'"
		@close="emit('close:address')"
	>
		<template #body>
			<div class="flex gap-2 flex-col px-4 w-full min-w-3xl mb-2 max-sm:max-h-[650px] overflow-y-auto bg-white">
				<span class="text-lg font-bold">{{ $t('form.location') }}</span>

				<div class="grid grid-cols-2 gap-6 w-full mt-4">
					<FormField
						v-slot="{ componentField }"
						name="cityId"
						class="col-span-1"
					>
						<FormItem class="w-full flex flex-col">
							<FormLabel class="font-bold">
								{{ $t('form.city') }}*
							</FormLabel>
							<FormControl>
								<select
									id="city"
									name="city"
									v-bind="componentField"
									class="text-sm !outline-none border border-gray-200 rounded px-2 py-1 h-11"
								>
									<option :value="null">
										{{ $t('form.select-city') }}
									</option>

									<option
										v-for="city in cites"
										:key="`city_id_${city.value}`"
										:value="city.value"
									>
										{{ city.text }}
									</option>
								</select>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormField
						v-slot="{ componentField }"
						name="district"
						class="col-span-1"
					>
						<FormItem class="w-full flex flex-col">
							<FormLabel class="font-bold">
								{{ $t('form.area') }}*
							</FormLabel>
							<FormControl>
								<Input
									class="h-11"
									type="text"
									:placeholder="$t('form.area')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormField
						v-slot="{ componentField }"
						name="fullAddress"
					>
						<FormItem class="w-full flex flex-col col-span-2">
							<FormLabel class="font-bold">
								{{ $t('form.full-address') }}*
							</FormLabel>
							<FormControl>
								<Input
									class="h-11"
									type="text"
									:placeholder="$t('form.full-address')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<div class="flex justify-between items-start gap-2 col-span-2">
						<div class="flex flex-col gap-2">
							<div class="flex gap-2 items-center">
								<span class="text-sm font-semibold">{{ $t('form.select-location') }}</span>
								<span class="text-gray-500 text-sm">({{ $t('form.optional') }})</span>
							</div>
							<div class="flex gap-4 items-center cursor-pointer mt-2">
								<div class="flex gap-2 items-center">
									<input
										id="house"
										v-model="Form.values.buildingType"
										type="radio"
										name="location"
										value="home"
										class="w-4 h-4"
									>

									<label
										for="house"
										class="text-sm"
									>
										{{ $t('form.house') }}
									</label>
								</div>
								<div class="flex gap-2 items-center">
									<input
										id="work"
										v-model="Form.values.buildingType"
										type="radio"
										name="location"
										value="work"
										class="w-4 h-4"
									>

									<label
										for="work"
										class="text-sm"
									>
										{{ $t('form.work') }}
									</label>
								</div>
							</div>
						</div>

						<div class="flex items-center gap-2 w-full justify-end">
							<label for="set-default">{{ $t('form.set-default-address') }}</label>
							<Switch
								:id="`set-default-${address?.addressId}`"
								v-model="isDefault"
								:disabled="isDisabledDefault"
								:class="{ 'opacity-50 pointer-event-none': isDisabledDefault }"
							/>
						</div>
					</div>

					<div class="flex col-span-2">
						<div class="flex w-full border-dashed border border-gray-200 my-2" />
					</div>

					<div class="flex w-full col-span-2 justify-between items-center max-sm:flex-col max-sm:items-start">
						<span class="text-lg font-bold text-nowrap">{{ $t('form.receipt-details') }}</span>

						<div class="flex items-center gap-2 w-full justify-end max-sm:justify-start ">
							<label
								:for="`is-shared-${address?.addressId}`"
								class="font-semibold"
							>
								{{ $t('form.use-user-address-details') }}
							</label>
							<Switch
								:id="`is-shared-${address?.addressId}`"
								v-model="isProfileDataShared"
							/>
						</div>
					</div>

					<template v-if="!isProfileDataShared">
						<FormPhone
							:title="$t('form.phone')"
							:error="Form.errors?.value?.phone"
							:value="address?.phone"
							@update="(value: FormPhoneValue) => {
								Form.setFieldValue('phone', {
									number: `${value.nationalNumber}`,
									code: value.countryCallingCode,
									iso: value.countryCode,
									isValid: value.isValid,
								})
							}"
						/>

						<div class="flex col-span-2 justify-between items-center gap-4">
							<FormField
								v-slot="{ componentField }"
								name="firstName"
							>
								<FormItem class="w-full flex flex-col col-span-2">
									<FormLabel class="font-bold">
										{{ $t('form.first-name') }}*
									</FormLabel>
									<FormControl>
										<Input
											class="h-11"
											type="text"
											:placeholder="$t('form.first-name')"
											v-bind="componentField"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							</FormField>

							<FormField
								v-slot="{ componentField }"
								name="lastName"
							>
								<FormItem class="w-full flex flex-col col-span-2">
									<FormLabel class="font-bold">
										{{ $t('form.last-name') }}*
									</FormLabel>
									<FormControl>
										<Input
											class="h-11"
											type="text"
											:placeholder="$t('form.last-name')"
											v-bind="componentField"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							</FormField>
						</div>
					</template>
				</div>
			</div>
		</template>

		<template #footer>
			<div class="flex justify-end gap-4 w-full my-2 max-sm:my-8 max-sm:justify-between items-center border-t pt-4">
				<Button
					variant="outline"
					class="max-sm:w-full"
					@click.prevent="() => emit('close:address')"
				>
					{{ $t('form.cancel') }}
				</Button>
				<Button
					class="max-sm:w-full"
					:loading="loading"
					@click="() => onSubmitChange()"
				>
					<span v-if="address.addressId">
						{{ $t('form.edit-address') }}
					</span>
					<span v-else>
						{{ $t('form.save-address') }}
					</span>
				</Button>
			</div>
		</template>
	</Modal>
</template>
