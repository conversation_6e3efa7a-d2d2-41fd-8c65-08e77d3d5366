# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: actionjo
# "app" enables Serverless Framework Dashboard features and sharing them with other Services.
app: action-v21-frontend-prod-app
service: action-v21-frontend
frameworkVersion: ~4.14.3

provider:
  name: aws
  region: eu-west-1
  runtime: nodejs22.x
  vpc:
    securityGroupIds:
      - sg-0c6b6715fcdd700c5
    subnetIds:
      - subnet-060dd3bb1ec187363 # copilot-v2-prod-priv0
      - subnet-0596eb12436cec3e5 # copilot-v2-prod-priv1
  stage: prod
  # Add API Gateway configuration
  httpApi:
    cors: true
    payload: '2.0'

package:
  patterns:
    - '!**'
    - '.output/**'

functions:
  app:
    handler: .output/server/index.handler
    url:
      cors:
        allowCredentials: false
    events:
      - httpApi: '*' # Catch all routes and forward to the Nuxt app
    timeout: 30
    memorySize: 1024
    environment:
      BASE_URL: ${env:BASE_URL}
      APP_URL: ${env:APP_URL}
      ENVIRONMENT: ${env:ENVIRONMENT}
      #HYPERPAY_URL: ${env:HYPERPAY_URL} # not being used so far
      #ENABLE_CACHE: ${env:ENABLE_CACHE}
      #DEBUG_CACHE: ${env:DEBUG_CACHE}
      #CACHE_TOKEN: ${env:CACHE_TOKEN}
      REDIS_HOST: ${env:REDIS_HOST}
      REDIS_PORT: ${env:REDIS_PORT}
      REDIS_DB: ${env:REDIS_DB}
      SENTRY_DSN: ${env:SENTRY_DSN}
      SENTRY_ORG: ${ env:SENTRY_ORG}
      SENTRY_PROJECT: ${env:SENTRY_PROJECT}
      SENTRY_AUTH_TOKEN: ${env:SENTRY_AUTH_TOKEN}
      GTM_ID: ${env:GTM_ID}
