<script setup lang="ts">
import type { NewArrival } from '~/interfaces/product/new-arrival'

const { data, error, status } = await useApi<NewArrival[]>('new-arrival', {
	query: { limit: 8 },
})

if (error.value) {
	console.error('Error fetching new arrival:', error.value)
}

const products = computed(() => data.value as NewArrival[])
const isLoading = computed(() => status.value === 'pending')
</script>

<template>
	<Card class="col-span-2 flex flex-col max-md:col-span-3 max-md:hidden">
		<div class="px-4 py-4 flex justify-between items-center">
			<h2 class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.new-arrival-title') }}
			</h2>

			<NuxtLinkLocale
				:to="`/category/smartphones`"
				class="text-primary-600 hidden max-sm:flex"
			>
				{{ $t('home.see-more') }}
			</NuxtLinkLocale>
		</div>
		<CardContent class="grid md:grid-cols-4 xs:grid-cols-1 gap-4 px-4 place-items-center md:min-h-[432px]">
			<template
				v-if="isLoading"
			>
				<div
					v-for="(_, index) in Array(8)"
					:key="`product-skeleton-${index}`"
					class="flex flex-col rounded h-full border p-2 border-gray-200 min-w-full"
				>
					<Skeleton class="w-full h-32 bg-gray-200 mb-2" />
					<Skeleton class="w-4/5 h-5 bg-gray-200 mb-2" />
					<Skeleton class="w-1/3 h-5 bg-gray-200 mb-2" />
				</div>
			</template>
			<template v-else>
				<ProductCard
					v-for="(product, index) in products"
					:key="`product-${index}`"
					:product="product"
					variant="sm"
					class="min-w-full xs:hidden md:block"
				/>
			</template>
		</CardContent>
	</Card>
	<horizantal-cards
		class="md:hidden"
		:title="$t('home.new-arrival-title')"
		:products="products"
		:loading="isLoading"
	/>
</template>
