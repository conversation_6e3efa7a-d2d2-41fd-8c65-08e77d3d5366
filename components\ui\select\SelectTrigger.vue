<script setup lang="ts">
import { SelectIcon, SelectTrigger, type SelectTriggerProps, useForwardProps } from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<SelectTriggerProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = computed(() => {
	const { class: _, ...delegated } = props

	return delegated
})

const forwardedProps = useForwardProps(delegatedProps)
const { locale } = useI18n()
const dir = computed(() => locale.value == 'ar' ? 'rtl' : 'ltr')
</script>

<template>
	<SelectTrigger
		:localw="locale"
		v-bind="forwardedProps"
		:class="cn(
			'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ground data-[placeholder]:text-muted-foreground !outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:truncate text-start',
			props.class,
		)"
	>
		<div
			:dir="dir"
			class="flex flex-grow min-w-full items-center justify-between"
		>
			<slot />
			<SelectIcon as-child>
				<Icon
					name="lucide:chevron-down"
					class="w-4 h-4 opacity-50 shrink-0"
				/>
			</SelectIcon>
		</div>
	</SelectTrigger>
</template>
