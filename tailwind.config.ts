import type { Config } from 'tailwindcss'
import tailwindcssAnimate from 'tailwindcss-animate'
import tailwindcssLineClamp from '@tailwindcss/line-clamp'

export default <Partial<Config>>{
	darkMode: ['class'],
	content: [
		'./components/**/*.{js,vue,ts}',
		'./layouts/**/*.vue',
		'./pages/**/*.vue',
		'./app.vue',
	],

	theme: {
		extend: {
			colors: {
				body: '#EBEFF8',
				primary: {
					100: '#F6F5F8',
					200: '#E3D4DF',
					300: '#EBD8E7',
					400: '#B885AD',
					500: '#9C3D88',
					600: '#893477',
					700: '#732A61',
					DEFAULT: 'hsl(var(--primary-600))',
					foreground: 'hsl(var(--primary-foreground-600))',
				},
				green: {
					1000: '#008000',
				},
				rating: {
					100: '#E1DDDD',
					200: '#38AE04',
				},
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))',
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))',
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))',
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))',
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))',
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))',
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				chart: {
					1: 'hsl(var(--chart-1))',
					2: 'hsl(var(--chart-2))',
					3: 'hsl(var(--chart-3))',
					4: 'hsl(var(--chart-4))',
					5: 'hsl(var(--chart-5))',
				},
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)',
			},
			fontSize: {
				'2xs': '0.625rem',
			},
			minHeight: {
				97: '26rem',
				98: '28rem',
				99: '30rem',
				100: '32rem',
			},
		},
		fontFamily: {
			sans: ['Cairo', 'sans-serif'],
		},
		screens: {
			xs: '110px',
			sm: '600px',
			md: '992px',
			lg: '1300px',
			xl: '1700px',
		},

		// screens: {
		// 	'sm': '110',
		// 	'md': '640px',
		// 	'lg': '992px',
		// 	'2xl': '1300px',
		// },
	},

	plugins: [tailwindcssAnimate, tailwindcssLineClamp],
}
