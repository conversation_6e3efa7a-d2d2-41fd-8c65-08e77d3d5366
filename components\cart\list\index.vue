<script setup lang="ts">
import type { Items } from '~/interfaces/cart/cart-list'
import type { QuantityPayload } from '~/interfaces/payload'
import { useCartStore } from '~/store/useCartStore'

const cartStore = useCartStore()
const list = ref<Items[]>([])
const listLength = computed<number>(() => list.value?.length)
const isListEmpty = computed<boolean>(() => !list.value?.length)
const loading = computed<boolean>(() => cartStore.fetching && !list.value?.length)

watch(() => cartStore, (value) => {
	list.value = [...value.list] as Items[]
}, { deep: true, immediate: true })

/**
 * Updates the quantity of a specific item in the cart.
 *
 * @param {QuantityPayload} payload - The data payload containing item details and the updated quantity.
 * @returns {Promise<void>} Resolves when the quantity is successfully updated.
 */
const updateQuantity = async (payload: QuantityPayload): Promise<void> => {
	await cartStore.updateQuantity(payload)
}

/**
 * Asynchronously removes an item from the cart list by its identifier.
 *
 * @param {number} cartId - The unique identifier of the cart item to be removed.
 * @returns {void} A promise that resolves when the item is removed.
 */
const onRemoveFromList = (cartId: number): void => {
	cartStore.removeFromList(cartId)
}
</script>

<template>
	<Card class="w-2/3 max-sm:w-full">
		<CardHeader>
			<span class="text-xl font-bold text-gray-600">
				{{
					$t('cart-list.title', {
						item: $t('wish-list.item', { count: listLength }), number: listLength,
					})
				}}
			</span>
		</CardHeader>
		<CardContent>
			<div class="flex flex-col gap-6">
				<template v-if="loading">
					<div class="flex w-full gap-4 flex-col">
						<div
							v-for="(_, index) in Array(4)"
							:key="index"
							class="flex w-full p-4 gap-2 border rounded-lg"
						>
							<div class="flex w-32 h-28">
								<Skeleton class="w-full h-full" />
							</div>
							<div class="flex flex-col flex-grow gap-2">
								<Skeleton class="w-full h-5" />
								<Skeleton class="w-1/2 h-5" />
								<Skeleton class="w-1/3 h-5" />
							</div>
						</div>
					</div>
				</template>
				<template v-else-if="!isListEmpty">
					<CartListCard
						v-for="(item) in list"
						:key="item.cartId"
						:product="item"
						@update:quantity="updateQuantity"
						@remove:cart="onRemoveFromList"
					/>
				</template>
				<template v-else>
					<div class="flex flex-col w-full h-full justify-center items-center gap-6 py-12">
						<Icon
							name="ui:empty-cart-list"
							class="w-full h-60"
						/>
						<div class="flex text-lg max-w-xs text-center font-bold">
							{{ $t('cart-list.empty-text') }}
						</div>

						<Button :as-child="true">
							<NuxtLinkLocale href="/">
								{{ $t('cart-list.empty-button') }}
							</NuxtLinkLocale>
						</Button>
					</div>
				</template>
			</div>
		</CardContent>
	</Card>
</template>

<style scoped lang="scss">

</style>
