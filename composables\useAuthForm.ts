import { PhonesRules } from '~/assets/resources/phones-rules'
import type { AuthForm } from '~/interfaces/auth/form'

export const useAuthForm = () => {
	const form = ref<AuthForm>({
		first_name: '',
		last_name: '',
		phone: {
			number: '',
			iso: 'JO',
			code: '962',
		},
		password: '',
		confirm_password: '',
		new_password: '',
		email: '',
	})

	const passwordRegex = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/
	const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

	const validatePhone = computed<boolean>(() => {
		if (!form.value.phone?.iso || !form.value.phone?.number) {
			return false
		}

		const countryData = PhonesRules[form.value.phone.iso]
		if (!countryData) {
			return false
		}

		const regexPattern = new RegExp(`^${countryData[2]}$`)
		return regexPattern.test(`${form.value.phone.number}`) as boolean
	})

	const validatePassword = computed<boolean>(() => {
		return passwordRegex.test(form.value.password)
	})

	const validateNewPassword = computed<boolean>(() => {
		return passwordRegex.test(form.value.new_password)
	})

	const validateConfirmPassword = computed<boolean>(() => {
		return form.value.password === form.value.confirm_password && validatePassword.value
	})

	const validateFName = computed<boolean>(() => {
		return form.value.first_name?.length >= 1
	})

	const validateLName = computed<boolean>(() => {
		return form.value.last_name?.length >= 1
	})

	const validateEmail = computed<boolean>(() => {
		return emailRegex.test(form.value.email)
	})

	const isLoginFormValid = computed<boolean>(() => {
		return validatePhone.value && validatePassword.value
	})

	const isSignupFormValid = computed<boolean>(() => {
		return validatePhone.value
			&& validatePassword.value
			&& validateFName.value
			&& validateLName.value
			&& validateConfirmPassword.value
	})

	const isValidChangePassForm = computed<boolean>(() => {
		return validatePassword.value && validateConfirmPassword.value && validateNewPassword.value
	})

	const passwordRule = computed<any>(() => {
		const rules = {}
		const password = form.value.new_password?.trim()
		if (!password) {
			return rules
		}

		if (/(?=.*[0-9])/.test(password)) {
			rules[1] = true
		}

		if (/.{8,}/.test(password)) {
			rules[2] = true
		}

		if (/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) {
			rules[3] = true
		}

		if (/(?=.*[a-z])(?=.*[A-Z])/.test(password)) {
			rules[4] = true
		}

		return rules
	})

	return {
		form,
		validatePhone,
		validatePassword,
		isLoginFormValid,
		validateFName,
		validateLName,
		validateConfirmPassword,
		isSignupFormValid,
		passwordRule,
		validateEmail,
		isValidChangePassForm,
	}
}
