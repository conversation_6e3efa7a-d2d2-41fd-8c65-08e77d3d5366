/**
 * Check is empty value for each Array, Object, string and Number
 * @param value
 * @returns
 */
export const isEmpty = (value: unknown) => {
	if (value === null || value === undefined) {
		return true
	}

	if (typeof value === 'object') {
		if (Array.isArray(value)) {
			return value.length === 0
		} else {
			return Object.keys(value).length === 0
		}
	}

	if (typeof value === 'string') {
		return value.trim().length === 0
	}

	if (typeof value === 'number') {
		return isNaN(value)
	}

	return false
}
