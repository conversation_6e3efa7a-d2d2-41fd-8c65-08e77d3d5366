import type { Details } from '~/interfaces/product/details'

interface Category {
	name: string
	slug: string
	parent?: Category | null
}

interface Breadcrumb {
	name: string
	path: string
	isLast?: boolean
}

export const useBreadcrumbs = () => {
	/**
	 * Build breadcrumbs from category and slugs.
	 * @param category - The root category object.
	 * @param basePath - The dynamic base URL (e.g., 'categories').
	 * @returns Array of breadcrumb objects.
	 */
	const buildBreadcrumbs = (
		category: Category,
		basePath: string,
	): Breadcrumb[] => {
		const breadcrumbs = []
		let currentCategory = category
		const categories = []

		/* First :: collect all categories in order */
		while (currentCategory) {
			categories.unshift({
				name: currentCategory.name,
				slug: currentCategory.slug,
			})
			currentCategory = currentCategory.parent || null
		}

		/* Second :: construct paths correctly */
		const pathSegments = [basePath]
		categories.forEach((cat, index) => {
			pathSegments.push(cat.slug)

			breadcrumbs.push({
				name: cat.name,
				slug: cat.slug,
				path: `/${pathSegments.join('/')}`,
				isLast: index === categories.length - 1,
			})
		})

		return breadcrumbs
	}

	const buildProductBreadcrumbs = (product: Details): Breadcrumb[] => {
		return [
			{
				name: product?.name,
				path: '/',
				isLast: true,
			},
		]
	}

	const buildSinglePage = (name: string): Breadcrumb[] => {
		return [
			{
				name,
				path: '/',
				isLast: true,
			},
		]
	}

	return {
		buildBreadcrumbs,
		buildProductBreadcrumbs,
		buildSinglePage,
	}
}
