<script setup lang="ts">
const { t } = useI18n()
/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	const { buildSinglePage } = useBreadcrumbs()
	return buildSinglePage(t('cart.page-title'))
})

definePageMeta({
	name: 'cart',
})
</script>

<template>
	<div class="flex flex-col">
		<Breadcrumb
			:links="breadCrumbLinks"
		/>
	</div>

	<div class="flex gap-6 w-full my-6 max-sm:flex-col">
		<CartList />
		<CartDetails />
	</div>
</template>

<style scoped lang="scss">

</style>
