<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { onMounted } from 'vue'
import { DropdownMenuTrigger } from '~/components/ui/dropdown-menu'
// import { Skeleton } from '~/components/ui/skeleton'
import type { LockupCategories } from '~/interfaces/lockup/categories'

const expandedList = ref<LockupCategories[]>([])
const { t, locale } = useI18n()
const { data, error } = await useApi<LockupCategories[]>('lookups-website/categories', {
	watch: [locale],
})

const $device = useDevice()

const getInitialWidth = (): number => {
	if ($device.isMobile) return 300
	if ($device.isTablet) return 600
	return 1200
}

const response = computed<LockupCategories[] | []>(() => data.value as LockupCategories[] ?? [])
// const loading = computed(() => status.value !== 'success')
const { width: screenWidth } = useWindowSize({
	initialWidth: getInitialWidth(),
})

const breakPoint = ref<number>(0)
if (error.value) {
	console.error('API Error: fetching lockup category', error.value)
}

/** init menu list **/
const initList = (): void => {
	if (screenWidth.value >= 1200) {
		breakPoint.value = 6
	} else if (screenWidth.value < 1200 && screenWidth.value >= 600) {
		breakPoint.value = 2
	} else if (screenWidth.value < 600) {
		breakPoint.value = 1
	}
	if (breakPoint.value) {
		expandedList.value = response.value?.slice(0, breakPoint.value) || []
		expandedList.value.push({
			text: t('header.other-sections'),
			children: response.value?.slice(breakPoint.value, response.value.length),
			value: 'OTHER_SECTION',
		})
	}
}

const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const localePath = useLocalePath()
initList()

onMounted(() => {
	nextTick(() => {
		initList()
	})

	watch([() => screenWidth, () => data], () => {
		initList()
	}, { deep: true })
})
</script>

<template>
	<div
		class="container w-full flex gap-4 text-gray-500 flex-wrap transition-all max-md:!px-2"
	>
		<!-- <template v-if="loading"> -->
		<!-- <Skeleton
			v-for="(_, index) in Array(breakPoint)"
			:key="`home-link-${index}`"
			as="div"
			class="w-32 h-10"
		/> -->
		<!-- </template>
		<template v-else> -->
		<NuxtLinkLocale
			class="hover:bg-gray-50 p-2 flex items-center text-md cursor-pointer rounded-lg"
			to="/offers"
		>
			<span class="pe-4 text-nowrap">{{ $t('footer.offers') }} </span>
		</NuxtLinkLocale>

		<template v-if="breakPoint">
			<NavigationMenu
				v-for="(item, index) in expandedList"
				:key="`${index}-${JSON.stringify(item.text)}`"
				:disable-hover-trigger="false"
				:dir="dir"
				:href="item?.meta?localePath(`/category/${item.meta?.slug}`):'#'"
			>
				<AppHeaderLinksList :item="item" />
			</NavigationMenu>
		</template>
		<!-- </template> -->
	</div>
</template>
