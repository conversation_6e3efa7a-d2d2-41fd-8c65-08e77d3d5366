import { defineStore } from 'pinia'
import type { Wishlist, WishlistResponse, Pagination } from '~/interfaces/wishlist/list'

export const useFavoriteStore = defineStore('favorite', {

	state: () => ({
		list: [] as Wishlist[],
		fetching: true as boolean,
		error: null,
		perPage: 24,
		pagination: null as Pagination,
	}),

	actions: {

		async fetch() {
			try {
				this.fetching = true
				const { data, error, status } = await useApi<WishlistResponse>(`wishlist?perPage=${this.perPage}`)
				this.fetching = false
				if (error.value) throw error.value

				if (status.value === 'success') {
					this.setResponse(data.value)
				}
			} catch (error) {
				console.log('Error on fetching wishlist', error.value)
			}
		},

		/**
		 * Fetches a list of wishlist items from the API and updates the state.
		 * Handles API response status and errors appropriately.
		 *
		 * @return {Promise<void>} A promise that resolves when the fetch and state updates are complete.
		 * @param page
		 */
		async fetchMounted(page = 1): Promise<void> {
			const { $api } = useNuxtApp()
			this.fetching = true
			return $api<WishlistResponse>(`wishlist?perPage=${this.perPage}`, {
				query: {
					page: page,
				},
			})
				.then((data) => {
					this.setResponse(data as WishlistResponse)
				})
				.catch((error) => {
					return console.error('Error fetching wishlist:', error)
				})
				.finally(() => {
					return nextTick(() => this.fetching = false)
				})
		},

		/**
		 * Adds a product to the wishlist by sending a POST request to the API.
		 *
		 * @param {number} productId - The ID of the product to be added to the wishlist.
		 * @return {Promise<void>} Resolves with the updated product list after the product is added.
		 */
		async addToList(productId: number): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<WishlistResponse>('/wishlist', {
				method: 'POST',
				body: {
					productId,
				},
			}).then((data) => {
				this.setResponse(data)
			})
		},

		/**
		 * Removes a product from the wishlist by its ID.
		 * @param {number} productId - The ID of the product to be removed from the wishlist.
		 * @return {Promise<void>} A promise that resolves to the updated wishlist after the product is removed.
		 */
		async removeFromList(productId: number): Promise<void> {
			const { $api } = useNuxtApp()
			return $api<WishlistResponse>(`/wishlist/${productId}`, {
				method: 'DELETE',
			}).then((data) => {
				this.setResponse(data)
			})
		},

		/**
		 * Set response
		 *
		 * @param {WishlistResponse} data
		 */
		setResponse(data: WishlistResponse): void {
			this.pagination = (data as WishlistResponse)?.pagination as Pagination
			this.list = (data as WishlistResponse)?.items as Wishlist[]
		},

		/**
		 * Checks if a product with the given productId exists in the wishlist.
		 *
		 * @param {number} productId - The ID of the product to check.
		 * @return {boolean} Returns true if the product exists in the wishlist, otherwise false.
		 */
		hasFav(productId: number): boolean {
			return this.list?.some((item: Wishlist) => item.productId === productId)
		},
	},

	getters: {
		listIds: ({ list }) => list?.map((item: Wishlist) => (item.productId as number)),
		count: ({ pagination }) => pagination?.total || 0,
		hasPagination: ({ pagination }) => pagination?.lastPage > 1,
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useFavoriteStore, import.meta.hot))
}
