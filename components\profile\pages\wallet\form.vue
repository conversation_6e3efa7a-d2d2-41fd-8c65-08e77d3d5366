<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'

// import { toast } from 'vue-sonner'
import type { User } from '~/interfaces/auth/auth'
import type { PaymentMethodResponse } from '~/interfaces/auth/payment-method'

const { user } = defineProps<{
	user: User
}>()

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

// const route = useRoute()
const { priceFormat, currency } = useCurrency()

const { t } = useI18n()
const section = ref('balance')
const PMResponse = ref<PaymentMethodResponse>(null)

const modalTitle = computed(() => section.value == 'balance'
	? t('wallet.add-balance-title')
	: t('wallet.payment-method-title'),
)

const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		amount: z.preprocess((val) => {
			if (val === '' || val === null || val === undefined) return undefined
			return val
		}, z.number({ required_error: t('error.balance') }).min(0.01, t('error.balance'))),
		cardNumber: z.string().regex(/^\d{16}$/, t('error.card-number')),
		expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/\d{2}$/, t('error.expiry')),
		cvv: z.string().regex(/^\d{3}$/, t('error.cvv')),
		cardHolder: z.string().min(1, t('error.card-holder')),
	})),

	initialValues: {
		amount: 0,
		cardNumber: null,
		expiryDate: null,
		cvv: null,
		cardHolder: null,
	},
})

const submitAmount = async () => {
	const { $api } = useNuxtApp()
	$api<never>('/wallet/payment-methods', {
		method: 'POST',
		body: {
			amount: Form.values.amount,
			paymentMethodId: 4,
		},
	}).then((data) => {
		section.value = 'cards'
		PMResponse.value = data as PaymentMethodResponse
	}).catch((error) => {
		console.error('Error on saving payment method', error)
	}).finally(() => {
		Form.resetForm()
	})
}

const onBack = () => {
	PMResponse.value = null
	section.value = 'balance'
}
</script>

<template>
	<Modal
		:title="modalTitle"
		:dismissible="false"
		@close="emit('close:modal')"
	>
		<template #body>
			<div class="flex p-4 flex-col gap-2">
				<!-- Balance Section -->
				<template v-if="section === 'balance'">
					<div class="flex p-4 gap-2 items-center rounded-lg bg-sky-50">
						<span class="flex">
							{{ $t('wallet.total-balance') }}
						</span>
						<span class="font-bold">
							{{ priceFormat(user.wallet?.value || 0) }}
						</span>
					</div>

					<div class="flex w-full border-dashed border border-gray-100 my-2" />
					<div class="flex flex-col gap-2">
						<label
							for="balance"
							class="text-sm font-bold"
						>{{ $t('wallet.add-price') }}*</label>
						<div class="relative flex w-full px-4 border-gray-200 border rounded-lg h-11 mb-4 items-center">
							<FormField
								v-slot="{ componentField }"
								name="amount"
							>
								<FormItem class="w-full flex flex-col">
									<FormControl>
										<Input
											:min="0.01"
											:minlength="0.01"
											step="0.01"
											pattern="^\d+(\.\d{1,2})?$"
											class="outline-none flex flex-grow no-spinner border-none px-0"
											type="number"
											v-bind="componentField"
										/>
									</FormControl>
									<div class="absolute top-9 start-0">
										<FormMessage />
									</div>
								</FormItem>
							</FormField>
							<div class="flex border-s ps-4 h-full items-center">
								{{ currency }}
							</div>
						</div>
					</div>
				</template>
				<!-- Card Section -->
				<template v-if="section === 'cards'">
					<HyperpayForm :params="PMResponse" />
				</template>
			</div>
		</template>
		<template #footer>
			<div class="flex justify-end w-full gap-4 mt-4 border-t border-gray-200 pt-4">
				<Button
					variant="outline"
					@click="emit('close:modal')"
				>
					{{ $t('form.cancel') }}
				</Button>

				<template v-if="section==='balance'">
					<Button
						:disabled="!Form.isFieldValid('amount')"
						@click.prevent="() => submitAmount()"
					>
						{{ $t("wallet.next") }}
					</Button>
				</template>
				<template v-else>
					<div class="flex flex-grow justify-end gap-2">
						<Button @click.prevent="onBack">
							{{ $t("form.back") }}
						</Button>
					</div>
				</template>
			</div>
		</template>
	</Modal>
</template>
