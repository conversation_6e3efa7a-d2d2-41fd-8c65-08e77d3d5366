export interface Country {
	name?: string
	flag?: string
	code?: string
	dial_code?: string
	currency?: string
}

export const countries: Country[] = [
	{
		name: 'Jordan',
		flag: '🇯🇴',
		code: 'JO',
		dial_code: '962',
		currency: 'JOD',
	},
	{
		name: 'Afghanistan',
		flag: '🇦🇫',
		code: 'AF',
		dial_code: '93',
		currency: 'AFN',
	},
	{
		name: 'Åland Islands',
		flag: '🇦🇽',
		code: 'AX',
		dial_code: '358',
		currency: 'EUR',
	},
	{
		name: 'Albania',
		flag: '🇦🇱',
		code: 'AL',
		dial_code: '355',
		currency: 'ALL',
	},
	{
		name: 'Algeria',
		flag: '🇩🇿',
		code: 'DZ',
		dial_code: '213',
		currency: 'DZD',
	},
	{
		name: 'American Samoa',
		flag: '🇦🇸',
		code: 'AS',
		dial_code: '1684',
		currency: 'USD',
	},
	{
		name: 'Andorra',
		flag: '🇦🇩',
		code: 'AD',
		dial_code: '376',
		currency: 'EUR',
	},
	{
		name: 'Angola',
		flag: '🇦🇴',
		code: 'AO',
		dial_code: '244',
		currency: 'AOA',
	},
	{
		name: '<PERSON><PERSON><PERSON>',
		flag: '🇦🇮',
		code: 'AI',
		dial_code: '1264',
		currency: 'XCD',
	},
	{
		name: 'Antarctica',
		flag: '🇦🇶',
		code: 'AQ',
		dial_code: '672',
		currency: 'AUD',
	},
	{
		name: 'Antigua and Barbuda',
		flag: '🇦🇬',
		code: 'AG',
		dial_code: '1268',
		currency: 'XCD',
	},
	{
		name: 'Argentina',
		flag: '🇦🇷',
		code: 'AR',
		dial_code: '54',
		currency: 'ARS',
	},
	{
		name: 'Armenia',
		flag: '🇦🇲',
		code: 'AM',
		dial_code: '374',
		currency: 'AMD',
	},
	{
		name: 'Aruba',
		flag: '🇦🇼',
		code: 'AW',
		dial_code: '297',
		currency: 'AWG',
	},
	{
		name: 'Australia',
		flag: '🇦🇺',
		code: 'AU',
		dial_code: '61',
		currency: 'AUD',
	},
	{
		name: 'Austria',
		flag: '🇦🇹',
		code: 'AT',
		dial_code: '43',
		currency: 'EUR',
	},
	{
		name: 'Azerbaijan',
		flag: '🇦🇿',
		code: 'AZ',
		dial_code: '994',
		currency: 'AZN',
	},
	{
		name: 'Bahamas',
		flag: '🇧🇸',
		code: 'BS',
		dial_code: '1242',
		currency: 'BSD',
	},
	{
		name: 'Bahrain',
		flag: '🇧🇭',
		code: 'BH',
		dial_code: '973',
		currency: 'BHD',
	},
	{
		name: 'Bangladesh',
		flag: '🇧🇩',
		code: 'BD',
		dial_code: '880',
		currency: 'BDT',
	},
	{
		name: 'Barbados',
		flag: '🇧🇧',
		code: 'BB',
		dial_code: '1246',
		currency: 'BBD',
	},
	{
		name: 'Belarus',
		flag: '🇧🇾',
		code: 'BY',
		dial_code: '375',
		currency: 'BYN',
	},
	{
		name: 'Belgium',
		flag: '🇧🇪',
		code: 'BE',
		dial_code: '32',
		currency: 'EUR',
	},
	{
		name: 'Belize',
		flag: '🇧🇿',
		code: 'BZ',
		dial_code: '501',
		currency: 'BZD',
	},
	{
		name: 'Benin',
		flag: '🇧🇯',
		code: 'BJ',
		dial_code: '229',
		currency: 'XOF',
	},
	{
		name: 'Bermuda',
		flag: '🇧🇲',
		code: 'BM',
		dial_code: '1441',
		currency: 'BMD',
	},
	{
		name: 'Bhutan',
		flag: '🇧🇹',
		code: 'BT',
		dial_code: '975',
		currency: 'INR',
	},
	{
		name: 'Bolivia, Plurinational State of Bolivia',
		flag: '🇧🇴',
		code: 'BO',
		dial_code: '591',
		currency: 'BOB',
	},
	{
		name: 'Bosnia and Herzegovina',
		flag: '🇧🇦',
		code: 'BA',
		dial_code: '387',
		currency: 'BAM',
	},
	{
		name: 'Botswana',
		flag: '🇧🇼',
		code: 'BW',
		dial_code: '267',
		currency: 'BWP',
	},
	{
		name: 'Bouvet Island',
		flag: '🇧🇻',
		code: 'BV',
		dial_code: '47',
		currency: 'NOK',
	},
	{
		name: 'Brazil',
		flag: '🇧🇷',
		code: 'BR',
		dial_code: '55',
		currency: 'BRL',
	},
	{
		name: 'British Indian Ocean Territory',
		flag: '🇮🇴',
		code: 'IO',
		dial_code: '246',
		currency: 'USD',
	},
	{
		name: 'Brunei Darussalam',
		flag: '🇧🇳',
		code: 'BN',
		dial_code: '673',
		currency: 'BND',
	},
	{
		name: 'Bulgaria',
		flag: '🇧🇬',
		code: 'BG',
		dial_code: '359',
		currency: 'BGN',
	},
	{
		name: 'Burkina Faso',
		flag: '🇧🇫',
		code: 'BF',
		dial_code: '226',
		currency: 'BFA',
	},
	{
		name: 'Burundi',
		flag: '🇧🇮',
		code: 'BI',
		dial_code: '257',
		currency: 'BIF',
	},
	{
		name: 'Cambodia',
		flag: '🇰🇭',
		code: 'KH',
		dial_code: '855',
		currency: 'KHR',
	},
	{
		name: 'Cameroon',
		flag: '🇨🇲',
		code: 'CM',
		dial_code: '237',
		currency: 'XAF',
	},
	{
		name: 'Canada',
		flag: '🇨🇦',
		code: 'CA',
		dial_code: '1',
		currency: 'CAD',
	},
	{
		name: 'Cape Verde',
		flag: '🇨🇻',
		code: 'CV',
		dial_code: '238',
		currency: 'CVE',
	},
	{
		name: 'Cayman Islands',
		flag: '🇰🇾',
		code: 'KY',
		dial_code: '345',
		currency: 'KYD',
	},
	{
		name: 'Central African Republic',
		flag: '🇨🇫',
		code: 'CF',
		dial_code: '236',
		currency: 'CAF',
	},
	{
		name: 'Chad',
		flag: '🇹🇩',
		code: 'TD',
		dial_code: '235',
		currency: 'CDF',
	},
	{
		name: 'Chile',
		flag: '🇨🇱',
		code: 'CL',
		dial_code: '56',
		currency: 'CLP',
	},
	{
		name: 'China',
		flag: '🇨🇳',
		code: 'CN',
		dial_code: '86',
		currency: 'CNY',
	},
	{
		name: 'Christmas Island',
		flag: '🇨🇽',
		code: 'CX',
		dial_code: '61',
		currency: 'AUD',
	},
	{
		name: 'Cocos (Keeling) Islands',
		flag: '🇨🇨',
		code: 'CC',
		dial_code: '61',
		currency: 'AUD',
	},
	{
		name: 'Colombia',
		flag: '🇨🇴',
		code: 'CO',
		dial_code: '57',
		currency: 'COP',
	},
	{
		name: 'Comoros',
		flag: '🇰🇲',
		code: 'KM',
		dial_code: '269',
		currency: 'COM',
	},
	{
		name: 'Congo',
		flag: '🇨🇬',
		code: 'CG',
		dial_code: '242',
		currency: 'CDF',
	},
	{
		name: 'Congo, Democratic Republic of the Congo',
		flag: '🇨🇩',
		code: 'CD',
		dial_code: '243',
		currency: 'CDF',
	},
	{
		name: 'Cook Islands',
		flag: '🇨🇰',
		code: 'CK',
		dial_code: '682',
		currency: 'NZD',
	},
	{
		name: 'Costa Rica',
		flag: '🇨🇷',
		code: 'CR',
		dial_code: '506',
		currency: 'CRC',
	},
	{
		name: 'Croatia',
		flag: '🇭🇷',
		code: 'HR',
		dial_code: '385',
		currency: 'HRK',
	},
	{
		name: 'Cuba',
		flag: '🇨🇺',
		code: 'CU',
		dial_code: '53',
		currency: 'CUP',
	},
	{
		name: 'Curaçao',
		flag: '🇨🇼',
		code: 'CW',
		dial_code: '599',
		currency: 'ANG',
	},
	{
		name: 'Cyprus',
		flag: '🇨🇾',
		code: 'CY',
		dial_code: '357',
		currency: 'CYP',
	},
	{
		name: 'Czech Republic',
		flag: '🇨🇿',
		code: 'CZ',
		dial_code: '420',
		currency: 'CZK',
	},
	{
		name: 'Denmark',
		flag: '🇩🇰',
		code: 'DK',
		dial_code: '45',
		currency: 'DKK',
	},
	{
		name: 'Djibouti',
		flag: '🇩🇯',
		code: 'DJ',
		dial_code: '253',
		currency: 'DJF',
	},
	{
		name: 'Dominica',
		flag: '🇩🇲',
		code: 'DM',
		dial_code: '1767',
		currency: 'XCD',
	},
	{
		name: 'Dominican Republic',
		flag: '🇩🇴',
		code: 'DO',
		dial_code: '1809',
		currency: 'DOP',
	},
	{
		name: 'Ecuador',
		flag: '🇪🇨',
		code: 'EC',
		dial_code: '593',
		currency: 'USD',
	},
	{
		name: 'Egypt',
		flag: '🇪🇬',
		code: 'EG',
		dial_code: '20',
		currency: 'EGP',
	},
	{
		name: 'El Salvador',
		flag: '🇸🇻',
		code: 'SV',
		dial_code: '503',
		currency: 'SVC',
	},
	{
		name: 'Equatorial Guinea',
		flag: '🇬🇶',
		code: 'GQ',
		dial_code: '240',
		currency: 'GNF',
	},
	{
		name: 'Eritrea',
		flag: '🇪🇷',
		code: 'ER',
		dial_code: '291',
		currency: 'ERN',
	},
	{
		name: 'Estonia',
		flag: '🇪🇪',
		code: 'EE',
		dial_code: '372',
		currency: 'EEK',
	},
	{
		name: 'Eswatini',
		flag: '🇸🇿',
		code: 'SZ',
		dial_code: '268',
		currency: 'SZL',
	},
	{
		name: 'Ethiopia',
		flag: '🇪🇹',
		code: 'ET',
		dial_code: '251',
		currency: 'ETB',
	},
	{
		name: 'Falkland Islands',
		flag: '🇫🇰',
		code: 'FK',
		dial_code: '500',
		currency: 'FKP',
	},
	{
		name: 'Faroe Islands',
		flag: '🇫🇴',
		code: 'FO',
		dial_code: '298',
		currency: 'DKK',
	},
	{
		name: 'Fiji',
		flag: '🇫🇯',
		code: 'FJ',
		dial_code: '679',
		currency: 'FJD',
	},
	{
		name: 'Finland',
		flag: '🇫🇮',
		code: 'FI',
		dial_code: '358',
		currency: 'EUR',
	},
	{
		name: 'France',
		flag: '🇫🇷',
		code: 'FR',
		dial_code: '33',
		currency: 'EUR',
	},
	{
		name: 'French Guiana',
		flag: '🇬🇫',
		code: 'GF',
		dial_code: '594',
		currency: 'EUR',
	},
	{
		name: 'French Polynesia',
		flag: '🇵🇫',
		code: 'PF',
		dial_code: '689',
		currency: 'XPF',
	},
	{
		name: 'Gabon',
		flag: '🇬🇦',
		code: 'GA',
		dial_code: '241',
		currency: 'GAB',
	},
	{
		name: 'Gambia',
		flag: '🇬🇲',
		code: 'GM',
		dial_code: '220',
		currency: 'GMD',
	},
	{
		name: 'Georgia',
		flag: '🇬🇪',
		code: 'GE',
		dial_code: '995',
		currency: 'GEL',
	},
	{
		name: 'Germany',
		flag: '🇩🇪',
		code: 'DE',
		dial_code: '49',
		currency: 'EUR',
	},
	{
		name: 'Ghana',
		flag: '🇬🇭',
		code: 'GH',
		dial_code: '233',
		currency: 'GHS',
	},
	{
		name: 'Gibraltar',
		flag: '🇬🇮',
		code: 'GI',
		dial_code: '350',
		currency: 'GIP',
	},
	{
		name: 'Greece',
		flag: '🇬🇷',
		code: 'GR',
		dial_code: '30',
		currency: 'EUR',
	},
	{
		name: 'Greenland',
		flag: '🇬🇱',
		code: 'GL',
		dial_code: '299',
		currency: 'DKK',
	},
	{
		name: 'Grenada',
		flag: '🇬🇩',
		code: 'GD',
		dial_code: '1473',
		currency: 'XCD',
	},
	{
		name: 'Guadeloupe',
		flag: '🇬🇺',
		code: 'GP',
		dial_code: '590',
		currency: 'EUR',
	},
	{
		name: 'Guam',
		flag: '🇬🇺',
		code: 'GU',
		dial_code: '1671',
		currency: 'USD',
	},
	{
		name: 'Guatemala',
		flag: '🇬🇹',
		code: 'GT',
		dial_code: '502',
		currency: 'GTQ',
	},
	{
		name: 'Guernsey',
		flag: '🇬🇬',
		code: 'GG',
		dial_code: '44',
		currency: 'GBP',
	},
	{
		name: 'Guinea',
		flag: '🇬🇳',
		code: 'GN',
		dial_code: '224',
		currency: 'GNF',
	},
	{
		name: 'Guinea-Bissau',
		flag: '🇬🇼',
		code: 'GW',
		dial_code: '245',
		currency: 'GNF',
	},
	{
		name: 'Guyana',
		flag: '🇬🇾',
		code: 'GY',
		dial_code: '592',
		currency: 'GYD',
	},
	{
		name: 'Haiti',
		flag: '🇭🇹',
		code: 'HT',
		dial_code: '509',
		currency: 'HTG',
	},
	{
		name: 'Heard Island and McDonald Islands',
		flag: '🇭🇲',
		code: 'HM',
		dial_code: '61',
		currency: 'AUD',
	},
	{
		name: 'Honduras',
		flag: '🇭🇳',
		code: 'HN',
		dial_code: '504',
		currency: 'HNL',
	},
	{
		name: 'Hong Kong',
		flag: '🇭🇰',
		code: 'HK',
		dial_code: '852',
		currency: 'HKD',
	},
	{
		name: 'Hungary',
		flag: '🇭🇺',
		code: 'HU',
		dial_code: '36',
		currency: 'HUF',
	},
	{
		name: 'Iceland',
		flag: '🇮🇸',
		code: 'IS',
		dial_code: '354',
		currency: 'ISK',
	},
	{
		name: 'India',
		flag: '🇮🇳',
		code: 'IN',
		dial_code: '91',
		currency: 'INR',
	},
	{
		name: 'Indonesia',
		flag: '🇮🇩',
		code: 'ID',
		dial_code: '62',
		currency: 'IDR',
	},
	{
		name: 'Iran',
		flag: '🇮🇷',
		code: 'IR',
		dial_code: '98',
		currency: 'IRR',
	},
	{
		name: 'Iraq',
		flag: '🇮🇶',
		code: 'IQ',
		dial_code: '964',
		currency: 'IQD',
	},
	{
		name: 'Ireland',
		flag: '🇮🇪',
		code: 'IE',
		dial_code: '353',
		currency: 'EUR',
	},
	{
		name: 'Israel',
		flag: '🇮🇱',
		code: 'IL',
		dial_code: '972',
		currency: 'ILS',
	},
	{
		name: 'Italy',
		flag: '🇮🇹',
		code: 'IT',
		dial_code: '39',
		currency: 'EUR',
	},
	{
		name: 'Jamaica',
		flag: '🇯🇲',
		code: 'JM',
		dial_code: '1876',
		currency: 'JMD',
	},
	{
		name: 'Japan',
		flag: '🇯🇵',
		code: 'JP',
		dial_code: '81',
		currency: 'JPY',
	},
	{
		name: 'Jersey',
		flag: '🇯🇪',
		code: 'JE',
		dial_code: '44',
		currency: 'GBP',
	},
	{
		name: 'Kazakhstan',
		flag: '🇰🇿',
		code: 'KZ',
		dial_code: '7',
		currency: 'KZT',
	},
	{
		name: 'Kenya',
		flag: '🇰🇪',
		code: 'KE',
		dial_code: '254',
		currency: 'KES',
	},
	{
		name: 'Kiribati',
		flag: '🇰🇮',
		code: 'KI',
		dial_code: '686',
		currency: 'AUD',
	},
	{
		name: 'Korea, North',
		flag: '🇰🇵',
		code: 'KP',
		dial_code: '850',
		currency: 'KPW',
	},
	{
		name: 'Korea, South',
		flag: '🇰🇷',
		code: 'KR',
		dial_code: '82',
		currency: 'KRW',
	},
	{
		name: 'Kuwait',
		flag: '🇰🇼',
		code: 'KW',
		dial_code: '965',
		currency: 'KWD',
	},
	{
		name: 'Kyrgyzstan',
		flag: '🇰🇬',
		code: 'KG',
		dial_code: '996',
		currency: 'KGS',
	},
	{
		name: 'Laos',
		flag: '🇱🇦',
		code: 'LA',
		dial_code: '856',
		currency: 'LAK',
	},
	{
		name: 'Latvia',
		flag: '🇱🇻',
		code: 'LV',
		dial_code: '371',
		currency: 'EUR',
	},
	{
		name: 'Lebanon',
		flag: '🇱🇧',
		code: 'LB',
		dial_code: '961',
		currency: 'LBP',
	},
	{
		name: 'Lesotho',
		flag: '🇱🇸',
		code: 'LS',
		dial_code: '266',
		currency: 'LSL',
	},
	{
		name: 'Liberia',
		flag: '🇱🇷',
		code: 'LR',
		dial_code: '231',
		currency: 'LRD',
	},
	{
		name: 'Libya',
		flag: '🇱🇾',
		code: 'LY',
		dial_code: '218',
		currency: 'LYD',
	},
	{
		name: 'Liechtenstein',
		flag: '🇱🇮',
		code: 'LI',
		dial_code: '423',
		currency: 'CHF',
	},
	{
		name: 'Lithuania',
		flag: '🇱🇹',
		code: 'LT',
		dial_code: '370',
		currency: 'LTL',
	},
	{
		name: 'Luxembourg',
		flag: '🇱🇺',
		code: 'LU',
		dial_code: '352',
		currency: 'EUR',
	},
	{
		name: 'Macau',
		flag: '🇲🇴',
		code: 'MO',
		dial_code: '853',
		currency: 'MOP',
	},
	{
		name: 'Madagascar',
		flag: '🇲🇬',
		code: 'MG',
		dial_code: '261',
		currency: 'MGA',
	},
	{
		name: 'Malawi',
		flag: '🇲🇼',
		code: 'MW',
		dial_code: '265',
		currency: 'MWK',
	},
	{
		name: 'Malaysia',
		flag: '🇲🇾',
		code: 'MY',
		dial_code: '60',
		currency: 'MYR',
	},
	{
		name: 'Maldives',
		flag: '🇲🇻',
		code: 'MV',
		dial_code: '960',
		currency: 'MVR',
	},
	{
		name: 'Mali',
		flag: '🇲🇱',
		code: 'ML',
		dial_code: '223',
		currency: 'CFA',
	},
	{
		name: 'Malta',
		flag: '🇲🇹',
		code: 'MT',
		dial_code: '356',
		currency: 'EUR',
	},
	{
		name: 'Marshall Islands',
		flag: '🇲🇭',
		code: 'MH',
		dial_code: '692',
		currency: 'USD',
	},
	{
		name: 'Martinique',
		flag: '🇲🇶',
		code: 'MQ',
		dial_code: '596',
		currency: 'EUR',
	},
	{
		name: 'Mauritania',
		flag: '🇲🇷',
		code: 'MR',
		dial_code: '222',
		currency: 'MRO',
	},
	{
		name: 'Mauritius',
		flag: '🇲🇺',
		code: 'MU',
		dial_code: '230',
		currency: 'MUR',
	},
	{
		name: 'Mayotte',
		flag: '🇾🇹',
		code: 'YT',
		dial_code: '262',
		currency: 'EUR',
	},
	{
		name: 'Mexico',
		flag: '🇲🇽',
		code: 'MX',
		dial_code: '52',
		currency: 'MXN',
	},
	{
		name: 'Micronesia',
		flag: '🇫🇲',
		code: 'FM',
		dial_code: '691',
		currency: 'USD',
	},
	{
		name: 'Moldova',
		flag: '🇲🇩',
		code: 'MD',
		dial_code: '373',
		currency: 'MDL',
	},
	{
		name: 'Monaco',
		flag: '🇲🇨',
		code: 'MC',
		dial_code: '377',
		currency: 'EUR',
	},
	{
		name: 'Mongolia',
		flag: '🇲🇳',
		code: 'MN',
		dial_code: '976',
		currency: 'MNT',
	},
	{
		name: 'Montenegro',
		flag: '🇲🇪',
		code: 'ME',
		dial_code: '382',
		currency: 'EUR',
	},
	{
		name: 'Montserrat',
		flag: '🇲🇸',
		code: 'MS',
		dial_code: '1664',
		currency: 'XCD',
	},
	{
		name: 'Morocco',
		flag: '🇲🇦',
		code: 'MA',
		dial_code: '212',
		currency: 'MAD',
	},
	{
		name: 'Mozambique',
		flag: '🇲🇿',
		code: 'MZ',
		dial_code: '258',
		currency: 'MZN',
	},
	{
		name: 'Myanmar',
		flag: '🇲🇲',
		code: 'MM',
		dial_code: '95',
		currency: 'MMK',
	},
	{
		name: 'Namibia',
		flag: '🇳🇦',
		code: 'NA',
		dial_code: '264',
		currency: 'NAD',
	},
	{
		name: 'Nauru',
		flag: '🇳🇷',
		code: 'NR',
		dial_code: '674',
		currency: 'AUD',
	},
	{
		name: 'Nepal',
		flag: '🇳🇵',
		code: 'NP',
		dial_code: '977',
		currency: 'NPR',
	},
	{
		name: 'Netherlands',
		flag: '🇳🇱',
		code: 'NL',
		dial_code: '31',
		currency: 'EUR',
	},
	{
		name: 'New Caledonia',
		flag: '🇳🇨',
		code: 'NC',
		dial_code: '687',
		currency: 'XPF',
	},
	{
		name: 'New Zealand',
		flag: '🇳🇿',
		code: 'NZ',
		dial_code: '64',
		currency: 'NZD',
	},
	{
		name: 'Nicaragua',
		flag: '🇳🇮',
		code: 'NI',
		dial_code: '505',
		currency: 'NIO',
	},
	{
		name: 'Niger',
		flag: '🇳🇪',
		code: 'NE',
		dial_code: '227',
		currency: 'XOF',
	},
	{
		name: 'Nigeria',
		flag: '🇳🇬',
		code: 'NG',
		dial_code: '234',
		currency: 'NGN',
	},
	{
		name: 'Niue',
		flag: '🇳🇺',
		code: 'NU',
		dial_code: '683',
		currency: 'NZD',
	},
	{
		name: 'Norfolk Island',
		flag: '🇳🇫',
		code: 'NF',
		dial_code: '672',
		currency: 'AUD',
	},
	{
		name: 'North Macedonia',
		flag: '🇲🇰',
		code: 'MK',
		dial_code: '389',
		currency: 'MKD',
	},
	{
		name: 'Northern Mariana Islands',
		flag: '🇲🇵',
		code: 'MP',
		dial_code: '1',
		currency: 'USD',
	},
	{
		name: 'Norway',
		flag: '🇳🇴',
		code: 'NO',
		dial_code: '47',
		currency: 'NOK',
	},
	{
		name: 'Oman',
		flag: '🇴🇲',
		code: 'OM',
		dial_code: '968',
		currency: 'OMR',
	},
	{
		name: 'Pakistan',
		flag: '🇵🇰',
		code: 'PK',
		dial_code: '92',
		currency: 'PKR',
	},
	{
		name: 'Palau',
		flag: '🇵🇼',
		code: 'PW',
		dial_code: '680',
		currency: 'USD',
	},
	{
		name: 'Panama',
		flag: '🇵🇦',
		code: 'PA',
		dial_code: '507',
		currency: 'PAB',
	},
	{
		name: 'Papua New Guinea',
		flag: '🇵🇬',
		code: 'PG',
		dial_code: '675',
		currency: 'PGK',
	},
	{
		name: 'Paraguay',
		flag: '🇵🇾',
		code: 'PY',
		dial_code: '595',
		currency: 'PYG',
	},
	{
		name: 'Peru',
		flag: '🇵🇪',
		code: 'PE',
		dial_code: '51',
		currency: 'PEN',
	},
	{
		name: 'Philippines',
		flag: '🇵🇭',
		code: 'PH',
		dial_code: '63',
		currency: 'PHP',
	},
	{
		name: 'Pitcairn Islands',
		flag: '🇵🇳',
		code: 'PN',
		dial_code: '870',
		currency: 'NZD',
	},
	{
		name: 'Poland',
		flag: '🇵🇱',
		code: 'PL',
		dial_code: '48',
		currency: 'PLN',
	},
	{
		name: 'Portugal',
		flag: '🇵🇹',
		code: 'PT',
		dial_code: '351',
		currency: 'EUR',
	},
	{
		name: 'Puerto Rico',
		flag: '🇵🇷',
		code: 'PR',
		dial_code: '1',
		currency: 'USD',
	},
	{
		name: 'Qatar',
		flag: '🇶🇦',
		code: 'QA',
		dial_code: '974',
		currency: 'QAR',
	},
	{
		name: 'Romania',
		flag: '🇷🇴',
		code: 'RO',
		dial_code: '40',
		currency: 'RON',
	},
	{
		name: 'Russia',
		flag: '🇷🇺',
		code: 'RU',
		dial_code: '7',
		currency: 'RUB',
	},
	{
		name: 'Rwanda',
		flag: '🇷🇼',
		code: 'RW',
		dial_code: '250',
		currency: 'RWF',
	},
	{
		name: 'Reunion',
		flag: '🇷🇪',
		code: 'RE',
		dial_code: '262',
		currency: 'EUR',
	},
	{
		name: 'Saint Barthelemy',
		flag: '🇧🇱',
		code: 'BL',
		dial_code: '590',
		currency: 'EUR',
	},
	{
		name: 'Saint Helena, Ascension and Tristan da Cunha',
		flag: '🇬🇸',
		code: 'SH',
		dial_code: '290',
		currency: 'SHP',
	},
	{
		name: 'Saint Kitts and Nevis',
		flag: '🇰🇳',
		code: 'KN',
		dial_code: '1 869',
		currency: 'XCD',
	},
	{
		name: 'Saint Lucia',
		flag: '🇱🇨',
		code: 'LC',
		dial_code: '1 758',
		currency: 'XCD',
	},
	{
		name: 'Saint Martin (French part)',
		flag: '🇲🇫',
		code: 'MF',
		dial_code: '590',
		currency: 'EUR',
	},
	{
		name: 'Saint Pierre and Miquelon',
		flag: '🇵🇲',
		code: 'PM',
		dial_code: '508',
		currency: 'EUR',
	},
	{
		name: 'Saint Vincent and the Grenadines',
		flag: '🇻🇨',
		code: 'VC',
		dial_code: '1 784',
		currency: 'XCD',
	},
	{
		name: 'Samoa',
		flag: '🇼🇸',
		code: 'WS',
		dial_code: '685',
		currency: 'WST',
	},
	{
		name: 'San Marino',
		flag: '🇸🇲',
		code: 'SM',
		dial_code: '378',
		currency: 'EUR',
	},
	{
		name: 'Sao Tome and Principe',
		flag: '🇸🇹',
		code: 'ST',
		dial_code: '239',
		currency: 'STN',
	},
	{
		name: 'Saudi Arabia',
		flag: '🇸🇦',
		code: 'SA',
		dial_code: '966',
		currency: 'SAR',
	},
	{
		name: 'Senegal',
		flag: '🇸🇳',
		code: 'SN',
		dial_code: '221',
		currency: 'XOF',
	},
	{
		name: 'Serbia',
		flag: '🇷🇸',
		code: 'RS',
		dial_code: '381',
		currency: 'RSD',
	},
	{
		name: 'Seychelles',
		flag: '🇸🇨',
		code: 'SC',
		dial_code: '248',
		currency: 'SCR',
	},
	{
		name: 'Sierra Leone',
		flag: '🇸🇱',
		code: 'SL',
		dial_code: '232',
		currency: 'SLL',
	},
	{
		name: 'Singapore',
		flag: '🇸🇬',
		code: 'SG',
		dial_code: '65',
		currency: 'SGD',
	},
	{
		name: 'Sint Maarten (Dutch part)',
		flag: '🇸🇽',
		code: 'SX',
		dial_code: '1 721',
		currency: 'ANG',
	},
	{
		name: 'Slovakia',
		flag: '🇸🇰',
		code: 'SK',
		dial_code: '421',
		currency: 'EUR',
	},
	{
		name: 'Slovenia',
		flag: '🇸🇮',
		code: 'SI',
		dial_code: '386',
		currency: 'EUR',
	},
	{
		name: 'Solomon Islands',
		flag: '🇸🇧',
		code: 'SB',
		dial_code: '677',
		currency: 'SBD',
	},
	{
		name: 'Somalia',
		flag: '🇸🇴',
		code: 'SO',
		dial_code: '252',
		currency: 'SOS',
	},
	{
		name: 'South Africa',
		flag: '🇿🇦',
		code: 'ZA',
		dial_code: '27',
		currency: 'ZAR',
	},
	{
		name: 'South Georgia and the South Sandwich Islands',
		flag: '🇬🇸',
		code: 'GS',
		dial_code: '500',
		currency: 'GBP',
	},
	{
		name: 'South Korea',
		flag: '🇰🇷',
		code: 'KR',
		dial_code: '82',
		currency: 'KRW',
	},
	{
		name: 'South Sudan',
		flag: '🇸🇸',
		code: 'SS',
		dial_code: '211',
		currency: 'SSP',
	},
	{
		name: 'Spain',
		flag: '🇪🇸',
		code: 'ES',
		dial_code: '34',
		currency: 'EUR',
	},
	{
		name: 'Sri Lanka',
		flag: '🇱🇰',
		code: 'LK',
		dial_code: '94',
		currency: 'LKR',
	},
	{
		name: 'Sudan',
		flag: '🇸🇩',
		code: 'SD',
		dial_code: '249',
		currency: 'SDG',
	},
	{
		name: 'Suriname',
		flag: '🇸🇷',
		code: 'SR',
		dial_code: '597',
		currency: 'SRD',
	},
	{
		name: 'Svalbard and Jan Mayen',
		flag: '🇸🇯',
		code: 'SJ',
		dial_code: '47',
		currency: 'NOK',
	},
	{
		name: 'Sweden',
		flag: '🇸🇪',
		code: 'SE',
		dial_code: '46',
		currency: 'SEK',
	},
	{
		name: 'Switzerland',
		flag: '🇨🇭',
		code: 'CH',
		dial_code: '41',
		currency: 'CHF',
	},
	{
		name: 'Syria',
		flag: '🇸🇾',
		code: 'SY',
		dial_code: '963',
		currency: 'SYP',
	},
	{
		name: 'Taiwan',
		flag: '🇹🇼',
		code: 'TW',
		dial_code: '886',
		currency: 'TWD',
	},
	{
		name: 'Tajikistan',
		flag: '🇹🇯',
		code: 'TJ',
		dial_code: '992',
		currency: 'TJS',
	},
	{
		name: 'Tanzania',
		flag: '🇹🇿',
		code: 'TZ',
		dial_code: '255',
		currency: 'TZS',
	},
	{
		name: 'Thailand',
		flag: '🇹🇭',
		code: 'TH',
		dial_code: '66',
		currency: 'THB',
	},
	{
		name: 'Timor-Leste',
		flag: '🇹🇱',
		code: 'TL',
		dial_code: '670',
		currency: 'USD',
	},
	{
		name: 'Togo',
		flag: '🇹🇬',
		code: 'TG',
		dial_code: '228',
		currency: 'XOF',
	},
	{
		name: 'Tokelau',
		flag: '🇹🇰',
		code: 'TK',
		dial_code: '690',
		currency: 'NZD',
	},
	{
		name: 'Tonga',
		flag: '🇹🇴',
		code: 'TO',
		dial_code: '676',
		currency: 'TOP',
	},
	{
		name: 'Trinidad and Tobago',
		flag: '🇹🇹',
		code: 'TT',
		dial_code: '1 868',
		currency: 'TTD',
	},
	{
		name: 'Tunisia',
		flag: '🇹🇳',
		code: 'TN',
		dial_code: '216',
		currency: 'TND',
	},
	{
		name: 'Turkey',
		flag: '🇹🇷',
		code: 'TR',
		dial_code: '90',
		currency: 'TRY',
	},
	{
		name: 'Turkmenistan',
		flag: '🇹🇲',
		code: 'TM',
		dial_code: '993',
		currency: 'TMT',
	},
	{
		name: 'Tuvalu',
		flag: '🇹🇻',
		code: 'TV',
		dial_code: '688',
		currency: 'AUD',
	},
	{
		name: 'Uganda',
		flag: '🇺🇬',
		code: 'UG',
		dial_code: '256',
		currency: 'UGX',
	},
	{
		name: 'Ukraine',
		flag: '🇺🇦',
		code: 'UA',
		dial_code: '380',
		currency: 'UAH',
	},
	{
		name: 'United Arab Emirates',
		flag: '🇦🇪',
		code: 'AE',
		dial_code: '971',
		currency: 'AED',
	},
	{
		name: 'United Kingdom',
		flag: '🇬🇧',
		code: 'GB',
		dial_code: '44',
		currency: 'GBP',
	},
	{
		name: 'United States',
		flag: '🇺🇸',
		code: 'US',
		dial_code: '1',
		currency: 'USD',
	},
	{
		name: 'Uruguay',
		flag: '🇺🇾',
		code: 'UY',
		dial_code: '598',
		currency: 'UYU',
	},
	{
		name: 'Uzbekistan',
		flag: '🇺🇿',
		code: 'UZ',
		dial_code: '998',
		currency: 'UZS',
	},
	{
		name: 'Vanuatu',
		flag: '🇻🇺',
		code: 'VU',
		dial_code: '678',
		currency: 'VUV',
	},
	{
		name: 'Vatican City',
		flag: '🇻🇦',
		code: 'VA',
		dial_code: '379',
		currency: 'EUR',
	},
	{
		name: 'Venezuela',
		flag: '🇻🇪',
		code: 'VE',
		dial_code: '58',
		currency: 'VES',
	},
	{
		name: 'Vietnam',
		flag: '🇻🇳',
		code: 'VN',
		dial_code: '84',
		currency: 'VND',
	},
	{
		name: 'Wallis and Futuna',
		flag: '🇼🇫',
		code: 'WF',
		dial_code: '681',
		currency: 'XPF',
	},
	{
		name: 'Yemen',
		flag: '🇾🇪',
		code: 'YE',
		dial_code: '967',
		currency: 'YER',
	},
	{
		name: 'Zambia',
		flag: '🇿🇲',
		code: 'ZM',
		dial_code: '260',
		currency: 'ZMW',
	},
	{
		name: 'Zimbabwe',
		flag: '🇿🇼',
		code: 'ZW',
		dial_code: '263',
		currency: 'ZWD',
	},
	{
		name: 'Saint Kitts and Nevis',
		flag: '🇰🇳',
		code: 'KN',
		dial_code: '1-869',
		currency: 'XCD',
	},
	{
		name: 'Saint Lucia',
		flag: '🇱🇨',
		code: 'LC',
		dial_code: '1-758',
		currency: 'XCD',
	},
	{
		name: 'Saint Vincent and the Grenadines',
		flag: '🇻🇨',
		code: 'VC',
		dial_code: '1-784',
		currency: 'XCD',
	},
]
