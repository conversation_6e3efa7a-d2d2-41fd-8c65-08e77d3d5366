<script setup lang="ts">
import { toast } from 'vue-sonner'

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

const route = useRoute()

const submitDelete = async () => {
	const { $api } = useNuxtApp()
	$api<unknown>(`/orders/${route.params.orderId}/canceled`, {
		method: 'POST',
	}).then(() => {
		toast.success('order.canceled-successfully')
		window.location.reload()
		emit('close:modal')
	}).catch((error) => {
		console.log(error)
	})
}
</script>

<template>
	<Modal
		:title="$t('orders.confirm-cancel-order')"
		:description="$t('orders.confirm-cancel-order-text')"
		@close="emit('close:modal')"
	>
		<template #footer>
			<div class="flex justify-end w-full gap-4 mt-4 border-t border-gray-200 pt-4">
				<Button
					variant="outline"
					@click="emit('close:modal')"
				>
					{{ $t('form.cancel') }}
				</Button>

				<Button
					variant="danger"
					@click.prevent="() => submitDelete()"
				>
					{{ $t("orders.cancel-order") }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
