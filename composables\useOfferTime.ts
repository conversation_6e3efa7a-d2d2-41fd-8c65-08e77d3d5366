import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

export function useOfferTime(targetDate: string | Date) {
	const { t } = useI18n()
	const timeLeft = ref('')

	/** Ensure we only run this on the client **/
	if (!import.meta.client) return ref('')

	/** Function to get a pluralization form based on count **/
	const getPluralForm = (count: number, unit: string = null) => {
		// minutes is a special case
		if (unit === 'minutes' && (count < 5 || count > 10)) {
			return 'other'
		}

		// seconds is a special case
		if (unit === 'seconds' && (count <= 10 && count > 2)) {
			return 'few'
		}

		// seconds is a special case
		if (unit === 'seconds' && count < 3) {
			return 'one'
		}

		if (count === 1) return 'one'
		if (count === 2) return 'few'
		if (count > 1) return 'many'
		return 'other'
	}

	const updateCountdown = () => {
		const parsedDate = new Date(targetDate)
		if (isNaN(parsedDate.getTime())) {
			timeLeft.value = t('time.expired')
			return
		}

		const now = Date.now()
		const diff = parsedDate.getTime() - now

		if (diff <= 0) {
			timeLeft.value = t('time.expired')
			return
		}

		const days = Math.floor(diff / (1000 * 60 * 60 * 24))
		const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
		const seconds = Math.floor((diff % (1000 * 60)) / 1000)

		/** Handle pluralization for days, hours, minutes, and seconds **/
		const dayLabel = t(`time.day.${getPluralForm(days)}`, { count: days })
		const hourLabel = t(`time.hours.${getPluralForm(hours)}`, { count: hours })
		const minuteLabel = t(`time.minutes.${getPluralForm(minutes, 'minutes')}`, { count: minutes })
		const secondLabel = t(`time.seconds.${getPluralForm(seconds, 'seconds')}`, { count: seconds })

		timeLeft.value = [
			`${days} ${dayLabel}`,
			`${hours} ${hourLabel}`,
			`${minutes} ${minuteLabel}`,
			`${seconds} ${secondLabel}`,
		].join(' : ')
	}

	let interval: NodeJS.Timeout | null = null

	onMounted(() => {
		if (!import.meta.client) return ref('')
		updateCountdown()
		interval = setInterval(updateCountdown, 1000)
	})

	onUnmounted(() => {
		if (interval) clearInterval(interval)
	})

	return computed(() => timeLeft.value)
}
