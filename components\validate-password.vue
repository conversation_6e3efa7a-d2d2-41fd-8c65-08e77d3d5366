<script setup lang="ts">
const { password } = defineProps<{
	password: string | null
}>()

interface Rules {
	'rule-1'?: boolean
	'rule-2'?: boolean
	'rule-3'?: boolean
	'rule-4'?: boolean
}

const passwordRules = computed<Rules | undefined>(() => {
	if (!password) {
		return
	}
	const value = password as string

	return {
		'rule-1': /[a-z]/.test(value) && /[A-Z]/.test(value),
		'rule-2': value.length >= 8,
		'rule-3': /\d/.test(value),
		'rule-4': /[^a-zA-Z0-9]/.test(value),
	}
})
</script>

<template>
	<div class="flex flex-col gap-3 text-sm">
		<span class="font-semibold">
			{{ $t('form.password-hint-title') }}
		</span>

		<ul class="text-sm text-gray-500">
			<li class="flex items-center gap-2">
				<Icon
					v-if="passwordRules?.['rule-1']"
					name="lucide:circle-check-big"
					class="text-green-600"
				/>
				<Icon
					v-else
					class="text-rose-500"
					name="lucide:x-circle"
				/>
				<span>{{ $t('password.rule-1') }}</span>
			</li>
			<li class="flex items-center gap-2">
				<Icon
					v-if="passwordRules?.['rule-2']"
					name="lucide:circle-check-big"
					class="text-green-600"
				/>
				<Icon
					v-else
					class="text-rose-500"
					name="lucide:x-circle"
				/>
				<span>
					{{ $t('password.rule-2') }}
				</span>
			</li>
			<li class="flex items-center gap-2">
				<Icon
					v-if="passwordRules?.['rule-3']"
					name="lucide:circle-check-big"
					class="text-green-600"
				/>
				<Icon
					v-else
					class="text-rose-500"
					name="lucide:x-circle"
				/>
				<span>{{ $t('password.rule-3') }}</span>
			</li>
			<li class="flex items-center gap-2">
				<Icon
					v-if="passwordRules?.['rule-4']"
					name="lucide:circle-check-big"
					class="text-green-600"
				/>
				<Icon
					v-else
					name="lucide:x-circle"
					class="text-rose-500"
				/>
				<span>{{ $t('password.rule-4') }}</span>
			</li>
		</ul>
	</div>
</template>
