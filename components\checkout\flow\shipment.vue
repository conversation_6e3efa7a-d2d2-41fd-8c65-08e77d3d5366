<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import type { CheckoutOrder } from '~/interfaces/checkout/order'
import type { Shipping } from '~/interfaces/checkout/shipping'

const { priceFormat } = useCurrency()
const { order } = defineProps<{
	order?: CheckoutOrder
}>()

const emit = defineEmits<{
	(event: 'set:flow', value: number): void
}>()

const { data, error, status } = await useApi<Shipping[]>(`/orders/${order?.orderId}/shipping-carriers`)

if (error.value) {
	console.log('error on fetching shipping list', error.value)
}

const shippingList = computed(() => data?.value as Shipping[])
const loading = computed<boolean>(() => status?.value !== 'success')
const selectedId = ref<number | null>(order?.shippingCarrierId || null)
const isSubmitting = ref<boolean>(false)
const isLocked = computed(() => order?.status !== 'draft')

const nextFlow = async () => {
	isSubmitting.value = true
	const { $api } = useNuxtApp()
	$api<unknown>(`/orders/${order.orderId}/shipping-carriers`, {
		method: 'POST',
		body: {
			shippingCarrierId: selectedId.value,
		},
	})
		.then(() => {
			emit('set:flow', 3)
		})
		.finally(() => {
			isSubmitting.value = false
		})
}
</script>

<template>
	<CardContent class="flex-grow p-4">
		<div class="grid grid-cols-2 w-full gap-6 max-md:grid-cols-1">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(4)"
					:key="index"
					class="flex w-full max-w-full border rounded-lg p-2 gap-2 flex-col"
				>
					<Skeleton class="w-full h-7" />
					<Skeleton class="w-full h-5" />
				</div>
			</template>

			<template
				v-for="item in shippingList"
				v-else
				:key="item.shippingCarrierId"
			>
				<button
					:disabled="isLocked"
					class="flex flex-col border rounded-lg gap-2 cursor-pointer text-start"
					:class="{ active: item.shippingCarrierId === selectedId }"
					@click="selectedId = item.shippingCarrierId"
				>
					<div class="flex gap-2 border-b p-2">
						<div class="check flex rounded-full w-5 h-5 border p-0.5">
							<div class="child flex w-full h-full rounded-full" />
						</div>

						<div class="font-bold text-base flex-grow">
							{{ item.label }}
						</div>

						<div class="font-bold">
							{{ priceFormat(item.price.value) }}
						</div>
					</div>
					<div class="flex w-full items-center text-sm gap-4 px-4 pb-2">
						<span class="w-18 text-gray-600">
							{{ $t('form.the-delivery') }}:
						</span>
						<span class="text-gray-700">
							{{ item?.description ?? 'N/A' }}
						</span>
					</div>
				</button>
			</template>
		</div>
	</CardContent>
	<CardFooter class="gap-4 justify-end">
		<Button
			variant="outline"
			class="sm:min-w-24 xs:min-w-1/2"
			@click.prevent="() => emit('set:flow', 1)"
		>
			{{ $t("form.prev") }}
		</Button>

		<Button
			:disabled="!selectedId"
			class="sm:min-w-24 xs:min-w-1/2"
			:loading="isSubmitting"
			@click.prevent="() => nextFlow()"
		>
			{{ $t("form.next") }}
		</Button>
	</CardFooter>
</template>

<style scoped lang="scss">
.active {
  @apply bg-primary-300/30 border-primary-500;
  .check {
    @apply border-primary-600;
    .child {
      @apply bg-primary-600
    }
  }
}
</style>
