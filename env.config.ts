import { z } from 'zod'
import 'dotenv/config'

/**
 * Environment Variables Schema
 * This file defines typed environment variables from GitHub environment
 */

// Define the schema for environment variables
const envSchema = z.object({
	// APP
	APP_URL: z.string().describe('Base URL of the application'),
	BASE_URL: z.string().describe('Base URL for API requests'),
	// Sentry Configuration
	SENTRY_DSN: z.string().optional().describe('Sentry Data Source Name for error tracking'),
	SENTRY_AUTH_TOKEN: z.string().default('').describe('Sentry authentication token for API access'),
	SENTRY_ORG: z.string().default('').describe('Sentry organization slug'),
	SENTRY_PROJECT: z.string().default('').describe('Sentry project slug'),

	ENVIRONMENT: z.enum(['development', 'staging', 'production']).default('development').describe('Node environment'),
	// Redis Configuration
	REDIS_HOST: z.string().optional().describe('Redis server host'),
	REDIS_PORT: z.string().optional().describe('Redis server port'),
	REDIS_DB: z.string().optional().default('0').describe('Redis database number'),
	// Google Tag Manager Configuration
	GTM_ID: z.string().optional().default('').describe('Google Tag Manager ID'),
	PLAYWRIGHT_BASE_URL: z.string().optional().default('http://localhost:3000').describe('Base URL for Playwright tests'),
})

// Type inference from schema
export type Env = z.infer<typeof envSchema>

// Parse and validate environment variables from GitHub
function parseEnv(): Env {
	// Debug: log all environment variables to see what's actually available
	// console.log('🌍 All environment variables:', Object.keys(process.env).filter(key =>
	// 	key.includes('SENTRY') || key.includes('NODE_ENV') || key.includes('GITHUB'),
	// ).reduce((obj, key) => {
	// 	obj[key] = process.env[key]
	// 	return obj
	// }, {} as Record<string, string | undefined>))

	try {
		const parsed = envSchema.parse(process.env)
		// console.log('✅ Parsed environment variables:', parsed)
		return parsed
	} catch (error) {
		if (error instanceof z.ZodError) {
			const errorMessage = error.errors
				.map(err => `${err.path.join('.')}: ${err.message}`)
				.join('\n')

			console.error('❌ GitHub Environment validation failed:', errorMessage)
			throw new Error(`GitHub Environment validation failed:\n${errorMessage}`)
		}
		throw error
	}
}

// Export validated environment variables from GitHub
export const env = parseEnv()

// Export individual environment variables for convenience
export const {
	SENTRY_DSN,
	SENTRY_AUTH_TOKEN,
	SENTRY_ORG,
	SENTRY_PROJECT,
	ENVIRONMENT,
} = env

// Type-safe environment variable getter
export function getEnvVar<K extends keyof Env>(key: K): Env[K] {
	return env[key]
}

// Check if environment is production
export const isProduction = process.env.ENVIRONMENT === 'production'
export const isDevelopment = process.env.ENVIRONMENT === 'development'
export const isStaging = process.env.ENVIRONMENT === 'staging'
