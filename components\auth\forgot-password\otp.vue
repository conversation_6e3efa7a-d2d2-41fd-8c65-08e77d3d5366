<script setup lang="ts">
import { toast } from 'vue-sonner'
import { useAuthStore } from '~/store/useAuthStore.client'

const emit = defineEmits<{
	(event: 'set:step', value: number): void
}>()

const otp = ref<number[]>([])
const isLoading = ref<boolean>(false)
const otpString = computed<string>(() => otp.value.join(''))
const authStore = useAuthStore()
const { t } = useI18n()

const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const phone = computed(() => authStore.forgotForm?.phone)
const userId = computed(() => authStore.forgotForm?.userData?.userId)

const phoneNumber = computed(() => {
	console.log('authStore.forgotForm', authStore.forgotForm)
	return [
		'+',
		phone.value?.code,
		phone.value?.number,
	].join('')
})

/** submit phone OTP **/
const submitPhoneOTP = async () => {
	isLoading.value = true
	return authStore.resetVerifyPhoneOTP({
		code: otpString.value as string,
		userId: userId.value as number,
	}).then(() => {
		toast.success(t('form.update-password-now'))
		emit('set:step', 3)
	}).finally(() => {
		nextTick(() => isLoading.value = false)
	})
}

defineExpose({
	submitForm: submitPhoneOTP,
	isLoading: isLoading.value,
})
</script>

<template>
	<div class="flex flex-col gap-4 px-4 pt-6">
		<div class="flex w-full justify-end">
			<button
				class="p-1 hover:bg-gray-200 rounded-lg flex items-center justify-center"
				@click="emit('set:step', 1)"
			>
				<Icon
					name="lucide:chevron-right"
					:class="{ 'rotate-180': isRtl }"
					size="30px"
				/>
			</button>
		</div>
		<div class="relative flex flex-col w-full gap-2 px-4 justify-center items-center">
			<Icon
				name="ui:phone-otp"
				size="120px"
			/>
			<div
				class="flex flex-col w-full py-4 gap-4 justify-center items-center"
			>
				<label
					for="phone"
					class="text-xl font-bold"
				>
					{{ $t('form.verify-phone-title') }}
				</label>
				<PinInput
					id="pin-input"
					v-model="otp"
					type="number"
					@complete="submitPhoneOTP"
				>
					<PinInputGroup>
						<PinInputInput
							v-for="(_, id) in 4"
							:key="`otp-${id}`"
							:index="id"
						/>
					</PinInputGroup>
				</PinInput>

				<div class="flex flex-col gap-1">
					<div class="flex items-center w-full justify-center text-gray-500 text-sm gap-2 text-center">
						<span>
							{{ $t('form.verify-phone-text', { phone: '' }) }}
							<span dir="ltr">
								{{ phoneNumber }}
							</span>
						</span>
					</div>
					<div class="flex items-center w-full justify-center gap-2 text-sm">
						<div class="text-gray-500 text-center">
							{{ $t('form.change-otp-phone-title') }}
							<button
								class="text-primary-500 hover:underline cursor-pointer"
								@click="() => emit('set:step', 1)"
							>
								{{ $t('form.change') }}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
