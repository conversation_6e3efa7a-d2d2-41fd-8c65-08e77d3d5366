<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { AuthResetPassPayload } from '~/interfaces/auth/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { t } = useI18n()

const emit = defineEmits<{
	(event: 'close:modal'): void
	(event: 'set:step', value: number): void
}>()

const newEye = ref(false)
const isLoading = ref(false)
const confirmEye = ref(false)
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const userId = computed<number>(() => {
	if (authStore.forgotForm?.userData?.userId) {
		return authStore.forgotForm.userData.userId
	}
	return 0
})

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		password: z.string()
			.min(7, t('error.required'))
			.regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/, {
				message: t('error.password-strength'),
			}),

		passwordConfirmation: z.string().min(7, t('error.required')),
		userId: z.number(),
	})
		.refine(data => data.password === data.passwordConfirmation, {
			path: ['passwordConfirmation'],
			message: t('error.passwords-not-match'),
		})),

	initialValues: {
		password: '',
		passwordConfirmation: '',
		userId: userId.value,
	},
})

const changePassword = Form.handleSubmit(async (values: AuthResetPassPayload): Promise<void> => {
	isLoading.value = true
	return authStore.resetPassword(values)
		.then(async () => {
			toast.success(t('form.password-changed-success'))
			emit('close:modal')
		})
		.finally(() => {
			nextTick(() => isLoading.value = false)
		})
})

defineExpose({
	submitForm: changePassword,
	isLoading,
})
</script>

<template>
	<form
		class="flex flex-col gap-4 py-4 min-h-96"
	>
		<div class="flex w-full justify-end">
			<button
				class="p-1 hover:bg-gray-200 rounded-lg flex items-center justify-center"
				@click="emit('set:step', 1)"
			>
				<Icon
					name="lucide:chevron-right"
					:class="{ 'rotate-180': isRtl }"
					size="30px"
				/>
			</button>
		</div>
		<input
			type="text"
			autocomplete="on"
			class="hidden"
		>

		<FormField
			v-slot="{ componentField }"
			name="password"
		>
			<FormItem class="w-full relative ">
				<FormLabel class="font-bold">
					{{ $t('form.new-password') }}*
				</FormLabel>
				<FormControl>
					<Input
						:type="!newEye?'password':'text'"
						:placeholder="$t('form.new-password')"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
				<div class="flex absolute top-8 end-4">
					<button
						class="p-1"
						@click="() => newEye = !newEye"
					>
						<Icon
							:name="newEye?'lucide:eye':'lucide:eye-off'"
							size="18px"
						/>
					</button>
				</div>
			</FormItem>
		</FormField>

		<ValidatePassword :password="Form.values.password" />
		<FormField
			v-slot="{ componentField }"
			name="passwordConfirmation"
		>
			<FormItem class="relative">
				<FormLabel class="font-bold">
					{{ $t('form.confirm-new-password') }}*
				</FormLabel>
				<FormControl>
					<Input
						:type="!confirmEye?'password':'text'"
						:placeholder="$t('form.confirm-new-password')"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
				<div class="flex absolute top-8 end-4">
					<button
						class="p-1"
						@click="() => confirmEye = !confirmEye"
					>
						<Icon
							:name="confirmEye?'lucide:eye':'lucide:eye-off'"
							size="18px"
						/>
					</button>
				</div>
			</FormItem>
		</FormField>
	</form>
</template>
