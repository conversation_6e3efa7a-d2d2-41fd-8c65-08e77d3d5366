export interface MostPopular {
	hasStock: boolean
	productId: number
	description: string
	media: Media
	type: string
	colors: Color[]
	isListed: boolean
	variance: Variance
	brandId: number
	minPrice: MaxPrice
	name: string
	avgRate: number
	maxPrice: MaxPrice
	SKU: string
	brand: string
	slug: string
	hasOffer: boolean
}

export interface Color {
	hexCode: string
	extra: Extra
	name: Name
	attributeOptionId: number
}

export interface Extra {
}

export interface Name {
	ar: string
	en: string
}

export interface MaxPrice {
	symbol: string
	currency: string
	currencyId: number
	value: number
}

export interface Media {
	cover: Cover[]
	gallery: Cover[]
}

export interface Cover {
	preview: string
	disk: string
	src: string
	fileSize: number
	id: number
	mimeType: string
	sort: number
}

export interface Variance {
	varianceId: number
	brandId: number
	name: string
	media: Media
	type: string
	stock: Stock
	slug: string
}

export interface Stock {
	note: string
	quantity: number
	supplierId: number
	isOffer: boolean
	price: MaxPrice
	stockId: number
	priceBeforeOffer: MaxPrice
	isPreOrder: boolean
	maxPerUser: number
}
