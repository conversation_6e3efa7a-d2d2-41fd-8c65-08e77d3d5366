<script setup lang="ts">
import type { BreadcrumbsLinks } from '~/interfaces/breadcrumbs'
import type { CategoryDetails } from '~/interfaces/category/details'
import type { RedirectResponse, RedirectResponseDetails } from '~/interfaces/product/details'

const { t } = useI18n()
const route = useRoute()
const slug = computed<string>(() => String(route.params.slug || ''))
const slugs = computed<string[]>(() => slug.value.split(','))
const config = useRuntimeConfig()
const page = computed<number>(() => Number(route.query?.page || 1))
const siteUrl = computed(() => `${config?.public?.siteUrl}${route?.path}${page.value > 1 ? `?page=${page.value}` : ''}`)
const pageNumber = computed(() => page.value > 1 ? `- ${t(`form.page-number`, { page: page.value })}` : '')
const showPrevBtn = ref(page.value > 1)
const img = useImage()

const activeSlug = computed(() => {
	if (Array.isArray(slugs.value)) {
		return [...slugs.value].pop()
	}

	return null
})

/** fetch details api **/
const { data, error: detailsError } = await useApi<CategoryDetails>(`/categories/${activeSlug.value}`, {
	query: {
		'slugs[]': slugs.value,
	},
	watch: [activeSlug],
	deep: true,
	immediate: true,
})

const category = computed(() => data.value as CategoryDetails)

/** In case of any error **/
if (detailsError.value?.statusCode === 404) {
	throw createError({
		statusCode: 404,
	})
}

if (category.value?.code === 301) {
	navigateTo(`/category/${(category.value)?.details?.redirectUrl}`, { redirectCode: 301 })
}

const { buildBreadcrumbs } = useBreadcrumbs()

const metaTitle = computed(() => {
	if (category.value?.metaTitle) {
		return `${category.value.metaTitle} ${pageNumber.value ?? ''}`
	}

	return ''// t?.('app.home-title')
})
const metaDescription = computed(() => {
	if (category.value?.metaDescription) {
		return `${category.value.metaDescription} ${pageNumber.value ?? ''}`
	}

	return ''// t?.('app.home-description')
})

const metaImage = computed(() => {
	const imagePath = category.value?.media?.cover?.src || ''

	if (imagePath) {
		return img(imagePath, {}, { provider: 'backend' })
	}

	return '/'
})

useSeoMeta({
	title: metaTitle,
	description: () => metaDescription.value,
	ogTitle: () => metaTitle.value,
	ogDescription: () => metaDescription.value,
	twitterTitle: () => metaTitle.value,
	twitterDescription: () => metaDescription.value,
	ogImage: () => metaImage.value,
	twitterImage: () => metaImage.value,
})

useHead({
	link: [
		{ rel: 'canonical', href: siteUrl.value },
		{ rel: 'twitter:card', href: siteUrl.value },
		{ rel: 'og:url', href: siteUrl.value },
	],

	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Listing
		:category="category"
		:breadcrumbs="buildBreadcrumbs(category as BreadcrumbsLinks, 'category')"
		:show-prev-btn="showPrevBtn"
	/>
</template>
