<script setup lang="ts">
import { PaginationLast, type PaginationLastProps } from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import {
	Button,
} from '@/components/ui/button'
import { cn } from '@/lib/utils'

const props = withDefaults(defineProps<PaginationLastProps & { class?: HTMLAttributes['class'] }>(), {
	asChild: true,
})

const delegatedProps = computed(() => {
	const { class: _, ...delegated } = props

	return delegated
})
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
</script>

<template>
	<PaginationLast v-bind="delegatedProps">
		<Button
			:class="cn('w-10 h-10 p-0', props.class)"
			variant="outline"
		>
			<slot>
				<Icon
					name="lucide:chevron-last"
					class="h-4 w-4 "
					:class="{ 'rotate-180': isRtl }"
				/>
			</slot>
		</Button>
	</PaginationLast>
</template>
