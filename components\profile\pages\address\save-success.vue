<script setup lang="ts">
const emit = defineEmits<{
	(event: 'close:address'): void
}>()
</script>

<template>
	<Modal
		size="max-w-lg"
		@close="emit('close:address')"
	>
		<template #body>
			<div class="flex w-full flex-col justify-center items-center gap-4 py-4 px-6">
				<Icon
					name="ui:success-save-address"
					size="186px"
				/>
				<div class="text-center w-80">
					<span class="font-bold">
						{{ $t('form.save-address-success') }}
					</span>
				</div>
			</div>
		</template>
		<template #footer>
			<Button
				class="w-full"
				@click="emit('close:address')"
			>
				{{ $t('form.finish') }}
			</Button>
		</template>
	</Modal>
</template>
