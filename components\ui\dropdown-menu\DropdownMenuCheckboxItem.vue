<script setup lang="ts">
import {
	DropdownMenuCheckboxItem,
	type DropdownMenuCheckboxItemEmits,
	type DropdownMenuCheckboxItemProps,
	DropdownMenuItemIndicator,
	useForwardPropsEmits,
} from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<DropdownMenuCheckboxItemProps & { class?: HTMLAttributes['class'] }>()
const emits = defineEmits<DropdownMenuCheckboxItemEmits>()

const delegatedProps = computed(() => {
	const { class: _, ...delegated } = props

	return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)

const { locale } = useI18n()
const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
</script>

<template>
	<DropdownMenuCheckboxItem
		:dir="dir"
		v-bind="forwarded"
		:class=" cn(
			'relative flex cursor-default select-none items-center rounded-sm py-1.5 pe-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
			props.class,
		)"
	>
		<span
			class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center"
			:class="{ '!left-auto !right-2': dir==='ltr' }"
		>
			<DropdownMenuItemIndicator>
				<Icon
					name="lucide:check"
					class="w-4 h-4 text-primary-600"
				/>
			</DropdownMenuItemIndicator>
		</span>
		<slot />
	</DropdownMenuCheckboxItem>
</template>
