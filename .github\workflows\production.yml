name: Deploy with Serverless
run-name: ${{ github.actor }} trigger serverless deployment 🚀 🟦

on:
   push:
      branches:
         - v2.1

jobs:
   deploy:
      runs-on: ubuntu-latest
      environment: production  # This gives access to "production" environment secrets

      steps:
         -  name: Checkout repository
            uses: actions/checkout@v4

         -  name: Setup Node.js
            uses: actions/setup-node@v4
            with:
               node-version: '22'  # or your project's version

         -  name: Install dependencies
            run: npm ci
            env:
               APP_URL: ${{ vars.APP_URL }}
               BASE_URL: ${{ vars.BASE_URL }}

         -  name: Set Node.js memory limit
            run: export NODE_OPTIONS="--max-old-space-size=4096"

         -  name: build
            run: npm run build:lambda
            env:
               NODE_OPTIONS: "--max-old-space-size=4096"
               APP_URL: ${{ vars.APP_URL }}
               BASE_URL: ${{ vars.BASE_URL }}
               ENVIRONMENT: ${{ vars.ENVIRONMENT }}
               REDIS_HOST: ${{ vars.REDIS_HOST }}
               REDIS_PORT: ${{ vars.REDIS_PORT }}
               REDIS_DB: ${{ vars.REDIS_DB }}
               SENTRY_DSN: ${{ vars.SENTRY_DSN }}
               SENTRY_ORG: ${{ vars.SENTRY_ORG }}
               SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
               SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
               GTM_ID: ${{vars.GTM_ID || ''}}
         -  name: Configure AWS credentials
            uses: aws-actions/configure-aws-credentials@v2
            with:
               aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
               aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               aws-region: eu-west-1

         -  name: Deploy with Serverless
            env:
               SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
               # or if using AWS credentials directly
               AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
               AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               APP_URL: ${{ vars.APP_URL }}
               BASE_URL: ${{ vars.BASE_URL }}
               ENVIRONMENT: ${{ vars.ENVIRONMENT }}
               REDIS_HOST: ${{ vars.REDIS_HOST }}
               REDIS_PORT: ${{ vars.REDIS_PORT }}
               REDIS_DB: ${{ vars.REDIS_DB }}
               SENTRY_DSN: ${{ vars.SENTRY_DSN }}
               SENTRY_ORG: ${{ vars.SENTRY_ORG }}
               SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
               SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
               GTM_ID: ${{vars.GTM_ID || ''}}
            run: npm run deploy
         -  name: Clear cache after deployment
            run: |
               curl --location '${{ vars.APP_URL }}/api/cache/clear' \
               --request POST \
               --header 'Content-Type: application/json' 