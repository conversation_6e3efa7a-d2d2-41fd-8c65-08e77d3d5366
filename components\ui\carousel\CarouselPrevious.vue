<script setup lang="ts">
import type { WithClassAsProps } from './interface'
import { useCarousel } from './useCarousel'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

const props = defineProps<WithClassAsProps>()

const { orientation, canScrollPrev, scrollPrev } = useCarousel()
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
</script>

<template>
	<Button
		:disabled="!canScrollPrev"
		:class="cn(
			'touch-manipulation absolute h-8 w-8  p-0 shadow',
			orientation === 'horizontal'
				? 'left-0 top-1/2 -translate-y-1/2'
				: '-top-12 left-1/2 -translate-x-1/2 rotate-90',
			isRtl ? 'rotate-180 left-auto right-0' : '',
			props.class,
		)"
		variant="white"
		@click="scrollPrev"
	>
		<slot>
			<Icon
				name="lucide:chevron-left"
				class="h-4 w-4 text-current"
			/>
			<span class="sr-only">Previous Slide</span>
		</slot>
	</Button>
</template>
