/**
 * Polyfill for String.prototype.replaceAll() method
 * Required for older browsers (Chrome < 85, Safari < 13.1, Firefox < 77)
 * This fixes base-vue-phone-input compatibility issues
 */

// Only add polyfill if the method doesn't exist
if (!String.prototype.replaceAll) {
	String.prototype.replaceAll = function (searchValue: string | RegExp, replaceValue: string | ((substring: string, ...args: unknown[]) => string)): string {
		// Handle RegExp case
		if (searchValue instanceof RegExp) {
			// Ensure global flag is set for RegExp
			if (!searchValue.global) {
				throw new TypeError('String.prototype.replaceAll called with a non-global RegExp argument')
			}
			// Use type assertion for RegExp case
			return this.replace(searchValue, replaceValue as (substring: string, ...args: unknown[]) => string)
		}

		// Handle string case - escape special regex characters and create global regex
		const escapedSearchValue = searchValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
		const regex = new RegExp(escapedSearchValue, 'g')

		// Use type assertion for string case
		if (typeof replaceValue === 'string') {
			return this.replace(regex, replaceValue)
		} else {
			return this.replace(regex, replaceValue as (substring: string, ...args: unknown[]) => string)
		}
	}
}

export default defineNuxtPlugin(() => {
	// This plugin doesn't need to do anything else,
	// the polyfill is applied when the file is loaded
})
