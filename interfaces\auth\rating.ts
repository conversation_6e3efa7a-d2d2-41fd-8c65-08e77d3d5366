export interface Rating {
	items?: Items[]
	pagination?: Pagination
}

export interface Pagination {
	total?: number
	count?: number
	perPage?: number
	page?: number
	lastPage?: number
}

export interface Items {
	ratingId?: number
	review?: string
	rating?: number
	userId?: number
	productId?: number
	status?: string
	createdAt?: string
	updatedAt?: string
	product?: Product
}

export interface Product {
	productId?: number
	name?: string
	slug?: string
	brandId?: number
	productGroupId?: number
	description?: string
	type?: string
	oldSlug?: string
	isPublished?: boolean
	isListed?: boolean
	variationAttributes?: number[]
	avgRate?: number
	createdAt?: string
	updatedAt?: string
	deletedAt?: string
	publishedAt?: string
	releaseAt?: string
	unPublishedAt?: string
	shippingId?: number
	priority?: number
	numberOfOrder?: number
	metaTitle?: string
	metaDescription?: string
	colors?: string
	hasStock?: boolean
	minPrice?: number
	maxPrice?: number
	hasOffer?: boolean
	minPriceBeforeOffer?: number
	maxPriceBeforeOffer?: number
	media?: Media
}

export interface Media {
	gallery?: Gallery[]
	cover?: Cover[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: string
}

export interface Cover {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: string
}
