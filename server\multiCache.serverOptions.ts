import { defineMultiCacheOptions } from 'nuxt-multi-cache/dist/runtime/serverOptions'
import redis from 'unstorage/drivers/redis'

const config = useRuntimeConfig()

export default defineMultiCacheOptions({
	api: {
		authorization: () => {
			return Promise.resolve(true)
		},
	},
	route: {
		applies(_path) {
			if (process.env.ENABLE_CACHE !== 'true') {
				return false
			}

			return true
		},
		storage: {
			driver: redis({
				tls: {
					host: config.redis.host,
					port: config.redis.port ? (config.redis.port) : undefined,
				},
				db: config.redis.db ? (config.redis.db) : undefined,
			}),
		},
	},

	data: {
		storage: {
			driver: redis({
				tls: {
					host: config.redis.host,
					port: config.redis.port ? (config.redis.port) : undefined,
				},
				db: config.redis.db ? (config.redis.db) : undefined,
			}),
		},
	},
})
