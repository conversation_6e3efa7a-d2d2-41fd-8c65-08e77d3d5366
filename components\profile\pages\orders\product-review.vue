<script setup lang="ts">
import { toast } from 'vue-sonner'

const form = ref({
	rating: 0,
	review: '',
})

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

const { t } = useI18n()
const route = useRoute()
const submitForm = async () => {
	const orderId = route.params.orderId
	const { $api } = useNuxtApp()
	return $api<unknown>(`/orders/${orderId}/rating`, {
		method: 'POST',
		body: {
			rating: form.value.rating,
			review: form.value.review,
		},
	}).then((data) => {
		toast.success(t('form.submit-rate-success'))
		emit('close:modal')
	}).catch((error) => {
		console.log(`Error on rating order ${error}`)
	})
}
</script>

<template>
	<Modal
		:title="$t('orders.review-experience-btn')"
		@close="emit('close:modal')"
	>
		<template #body>
			<div class="flex w-full flex-col px-4 gap-4">
				<div class="flex w-full flex-col h-48 bg-sky-50 justify-center items-center gap-4 rounded-lg">
					<div class="flex gap-2 flex-col justify-center items-center">
						<Icon
							name="ui:review-stars"
							size="25px"
						/>

						<Icon
							name="ui:product-review"
							size="50px"
						/>
					</div>
					<div class="flex justify-center items-center">
						<span class="text-base ">{{ $t('orders.product-review-text') }}</span>
					</div>
				</div>
				<div class="flex flex-col gap-4">
					<div class="flex flex-col gap-2 w-full">
						<span class="text-lg">{{ $t('product.rate') }}:</span>
						<div class="flex w-full gap-2">
							<button
								v-for="(_, index) in Array(5)"
								:key="`form-rating-${index}`"
								:class="form.rating >= 1 + index ? 'text-rating-200' : 'text-rating-100'"
								@click.prevent="() => form.rating = 1 + index"
							>
								<Icon
									name="ui:rate-star"
									size="30px"
								/>
							</button>
						</div>
					</div>

					<textarea
						v-model="form.review"
						name="description"
						:placeholder="$t('form.rate-description-placeholder')"
						class="border border-gray-300 rounded-md text-sm font-medium text-gray-500 p-3 outline-none resize-none"
						rows="5"
					/>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="flex justify-end w-full gap-4 mt-4">
				<Button
					variant="outline"
					@click="emit('close:modal')"
				>
					{{ $t('form.cancel') }}
				</Button>

				<Button @click.prevent="() => submitForm()">
					{{ $t('form.submit-rate-title') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>
