import { useNuxtApp } from '#app'

export const useCurrency = () => {
	const currency = computed(() => {
		const { $i18n } = useNuxtApp()
		return $i18n.locale?.value === 'en' ? 'JOD' : 'د.أ.'
	})
	/**
	 * Price Formatter
	 * @param price
	 * @param round
	 * @returns {string}
	 */
	const priceFormat = (
		price: number = 0,
		round: number = 2,
	): string => {
		const { $i18n } = useNuxtApp()
		const local: string = $i18n.locale?.value === 'en' ? 'en-US' : 'ar-JO'
		const currency: string = 'JOD'

		if (isNaN(price) || price === 0) {
			return $i18n.locale?.value === 'ar' ? '0.00 د.أ.' : '0.00 JOD'
		}

		const hasDecimals = price % 1 !== 0
		const formattedPrice = new Intl.NumberFormat(local, {
			style: 'currency',
			currency: currency,
			minimumFractionDigits: (hasDecimals ? round : 0),
			maximumFractionDigits: round,
		}).format(price)

		if ($i18n.locale?.value !== 'en') {
			return formattedPrice.replace(currency, $i18n.t(currency))
		}

		return formattedPrice
	}

	/**
	 * Price Number
	 * @param price
	 * @param round
	 */
	const priceNumber = (price = 0, round = 2) => {
		if (isNaN(price) || !price) return 0

		const { $i18n } = useNuxtApp()
		const locale = $i18n.locale?.value === 'en' ? 'en-US' : 'ar-JO'
		const hasDecimals = price % 1 !== 0

		return new Intl.NumberFormat(locale, {
			minimumFractionDigits: (hasDecimals ? round : 0),
			maximumFractionDigits: round,
			useGrouping: true,
		}).format(price)
	}

	return {
		currency,
		priceFormat,
		priceNumber,
	}
}
