<script setup lang="ts">
import FilterList from './index.vue'
import type { Filters as CategoryFilters } from '~/interfaces/filter/filters'
import {
	<PERSON>er,
	<PERSON>er<PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON><PERSON>ger,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON>er<PERSON>eader,
	DrawerTitle,
} from '@/components/ui/drawer'

const { total, filter, loading, update } = defineProps<{
	total: number
	filter?: CategoryFilters | unknown
	loading?: boolean
	update?: (value: string | number | null | object) => void
}>()

const filterData = ref(null)
const isFilterVisible = ref(false)
const isSortingVisible = ref(false)

const onFilterUpdated = (selected: object) => {
	filterData.value = {
		...selected,
	}
}

const onSubmitFilter = () => {
	isFilterVisible.value = false
	isSortingVisible.value = false
	nextTick(() => {
		update(filterData.value)
	})
}
</script>

<template>
	<div class="flex w-full items-center gap-2">
		<Drawer
			id="main-filter"
			v-model:open="isFilterVisible"
			:default-close="true"
		>
			<DrawerTrigger>
				<Button
					variant="icon"
					size="sm"
				>
					<Icon
						name="lucide:sliders-horizontal"
						size="16px"
					/>
				</Button>
			</DrawerTrigger>
			<DrawerContent
				:aria-describedby="$t('filters.title') "
				:has-hand="true"
			>
				<DrawerHeader>
					<DrawerTitle>{{ $t('filters.title') }}</DrawerTitle>
				</DrawerHeader>
				<div class="flex flex-col w-full max-h-[85vh] h-full overflow-y-auto">
					<FilterList
						:filter="filter"
						:loading="loading"
						:timeout="1"
						:update="onFilterUpdated"
					/>
				</div>
				<DrawerFooter class="flex flex-row items-center gap-2 justify-between">
					<Button
						variant="default"
						class="w-1/2"
						@click="onSubmitFilter"
					>
						{{ $t('form.save') }}
					</Button>
					<Button
						variant="outline"
						class="w-1/2"
						@click="isFilterVisible = false"
					>
						{{ $t('form.cancel') }}
					</Button>
				</DrawerFooter>
			</DrawerContent>
		</Drawer>

		<Drawer
			id="sorting-filter"
			v-model:open="isSortingVisible"
		>
			<DrawerTrigger>
				<Button
					variant="icon"
					size="sm"
				>
					<Icon
						name="lucide:list-filter"
						size="20px"
					/>
				</Button>
			</DrawerTrigger>
			<DrawerContent
				:aria-describedby="$t('filters.sorting-title') "
				has-hand
			>
				<DrawerHeader>
					<DrawerTitle>{{ $t('filters.sorting-title') }}</DrawerTitle>
				</DrawerHeader>
				<div class="flex flex-col w-full overflow-y-auto">
					<FilterSorting
						:filter="filter"
						:loading="loading"
						@update:filter="onFilterUpdated"
					/>
				</div>
				<DrawerFooter class="flex flex-row items-center gap-2 justify-between">
					<Button
						variant="default"
						class="w-1/2"
						@click="onSubmitFilter"
					>
						{{ $t('form.save') }}
					</Button>
					<Button
						variant="outline"
						class="w-1/2"
						@click="isSortingVisible = false"
					>
						{{ $t('form.cancel') }}
					</Button>
				</DrawerFooter>
			</DrawerContent>
		</Drawer>

		<span class="text-sm font-normal">{{ total }} {{ $t('filters.result-title') }}</span>
	</div>
</template>
