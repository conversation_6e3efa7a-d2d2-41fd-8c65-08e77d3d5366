<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { Primitive, type PrimitiveProps } from 'reka-ui'
import { type ButtonVariants, buttonVariants } from '.'
import { cn } from '@/lib/utils'

interface Props extends PrimitiveProps {
	variant?: ButtonVariants['variant']
	size?: ButtonVariants['size']
	class?: HTMLAttributes['class']
	loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	variant: 'default',
	as: 'button',
	loading: false,
})

const loaderColor = computed(() => ['default', 'danger'].includes(props.variant) ? '#fff' : '#9C3D88')
</script>

<template>
	<Primitive
		:as="as"
		:as-child="asChild"
		:class="cn(buttonVariants({ variant, size }), props.class, { relative: loading })"
		:variant="variant"
		:disabled="loading || undefined"
	>
		<template v-if="!loading">
			<slot />
		</template>
		<Spinner
			v-if="loading"
			class="absolute inset-0 m-auto w-5 h-5"
			:color="loaderColor"
		/>
	</Primitive>
</template>
