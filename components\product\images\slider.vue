<script setup lang="ts">
import { watchOnce } from '@vueuse/core'
import { ref } from 'vue'
import { toast } from 'vue-sonner'
import type { Cover, Details } from '~/interfaces/product/details'
import {
	Carousel,
	type CarouselApi,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
} from '@/components/ui/carousel'

interface Props {
	images?: Cover[]
	loading?: boolean
	isFullScreen?: boolean
	product: Details
}

const { images = [], loading, isFullScreen = false, product } = defineProps<Props>()
const { t } = useI18n()

const emit = defineEmits<{
	(event: 'set:preview', value: boolean): void
}>()

const sliderMainApi = ref<CarouselApi>()
const sliderThumbnailApi = ref<CarouselApi>()
const selectedIndex = ref(0)
const scale = ref(1)
const MIN_SCALE = 0.5
const MAX_SCALE = 2

const route = useRoute()

/** Share product details **/
const onShare = (): void => {
	const shareData = {
		title: product.metaTitle,
		text: product.metaDescription,
		url: route.fullPath,
	}

	if (navigator.canShare && navigator.canShare(shareData)) {
		navigator.share(shareData)
		return
	}

	const fallbackText = shareData.url
	navigator.clipboard.writeText(fallbackText).then(() => {
		toast.success(t('form.copy-success'))
	})
}

/**
 * On Select image
 */
const onSelect = () => {
	// reset scale
	scale.value = 1

	if (!sliderMainApi.value || !sliderThumbnailApi.value) {
		return
	}

	selectedIndex.value = sliderMainApi.value.selectedScrollSnap()
	sliderThumbnailApi.value.scrollTo(sliderMainApi.value.selectedScrollSnap())
}

/**
 * On a small image clicked
 * @param index
 */
const onThumbClick = (index: number) => {
	if (!sliderMainApi.value || !sliderThumbnailApi.value) {
		return
	}

	sliderMainApi.value.scrollTo(index)
}

/**
 * Watch on the slider
 */
watchOnce(sliderMainApi, (mainApi) => {
	if (!mainApi) {
		return
	}

	onSelect()
	mainApi.on('select', onSelect)
	mainApi.on('reInit', onSelect)
})

/** Set Zoom In **/
const zoomIn = (): void => {
	if (scale.value < MAX_SCALE) {
		scale.value += 0.1
	}
}

/** Set Zoom Out **/
const zoomOut = (): void => {
	if (scale.value > MIN_SCALE) {
		scale.value -= 0.1
	}
}

const hasImages = computed(() => !!images.length)
</script>

<template>
	<div
		class="flex max-w-4xl w-full flex-col items-start mx-auto select-none"
		:class="{ 'sm:h-svh py-12 justify-center': isFullScreen }"
	>
		<div
			class="z-0 relative flex w-full aspect-auto justify-center items-center bg-gray-100 rounded-lg"
		>
			<div
				v-if="!isFullScreen && hasImages && !loading"
				class="absolute start-4 top-4 flex flex-col gap-2 z-10"
			>
				<Button
					variant="icon"
					class="bg-white p-0 shadow hover:shadow-lg"
				>
					<Icon
						name="ui:share"
						class=" text-gray-800"
						size="18px"
						@click="onShare"
					/>
				</Button>

				<Button
					variant="icon"
					class="bg-white p-0 shadow hover:shadow-lg"
					@click.prevent="() => emit('set:preview', true)"
				>
					<Icon
						name="ui:expand"
						class="text-gray-800"
						size="18px"
					/>
				</Button>
			</div>
			<Carousel
				class="relative max-w-full py-10 rounded-lg overflow-hidden min-h-96 w-full z-0 justify-center"
				:class="{ 'py-0 min-h-full': isFullScreen, 'flex items-center': !isFullScreen }"
				@init-api="(val) => sliderMainApi = val"
			>
				<template v-if="loading">
					<div class="absolute top-0 left-0 z-10 w-full h-full bg-gray-100">
						<Skeleton class="w-full h-full min-w-full" />
					</div>
				</template>
				<CarouselContent class="!m-0 items-center">
					<CarouselItem
						v-for="(image, index) in images"
						:key="`big-image-${image.id}`"
						class="p-0 bg-gray-100"
						:class="{ 'z-10': index === selectedIndex }"
					>
						<div
							class="flex items-center justify-center rounded-lg"
							:class="{ 'h-full': isFullScreen }"
							:style="{ scale: scale }"
						>
							<NuxtImg
								:src="image.preview"
								:alt="product.metaTitle"
								:title="product.metaTitle"
								class="object-contain w-full max-w-sm relative"
								:class="{ 'min-h-full min-w-[99%] max-h-[700px]': isFullScreen }"
								sizes="xs:100vw md:1000px"
								width="400"
								height="400"
								format="webp"
								fit="contain"
								quality="100"
								provider="backend"
								:preload="!index"
								:loading="!index?'eager':'lazy'"
							/>
						</div>
					</CarouselItem>
				</CarouselContent>
				<template v-if="isFullScreen">
					<CarouselNext class="mx-5 fixed" />
					<CarouselPrevious class="mx-5 fixed" />
					<Button
						variant="icon"
						class="fixed top-0 start-0 ms-5 mt-5 size-6 bg-black"
						@click.prevent="() => emit('set:preview', false)"
					>
						<Icon
							name="lucide:circle-x"
							class="text-white"
							size="18px"
						/>
					</Button>

					<Button
						variant="icon"
						class="fixed top-0 start-20 mt-5 size-6 group max-w-20 p-0  bg-black"
						:disabled="scale >= MAX_SCALE"
						@click.prevent="zoomIn"
					>
						<Icon
							name="lucide:zoom-in"
							class="text-white group-disabled:opacity-75"
							size="20px"
						/>
					</Button>
					<Button
						variant="icon"
						class="fixed top-0 start-28 mt-5 size-6 group max-w-20 p-0 bg-black"
						:disabled="scale <= MIN_SCALE"
						@click.prevent="zoomOut"
					>
						<Icon
							name="lucide:zoom-out"
							class="text-white group-disabled:opacity-75 "
							size="20px"
						/>
					</Button>

					<div class="fixed top-0 end-0 m-5 bg-black rounded-lg px-1">
						<span class="text-md text-white font-semibold ">
							{{ 1 + selectedIndex }} / {{ images?.length }}
						</span>
					</div>
				</template>
				<div
					v-if="!isFullScreen && hasImages"
					class="flex absolute start-2 bottom-0 w-full"
				>
					<div
						v-if="!loading"
						class="flex relative w-[4.5rem] h-14 px-2 "
						:class="{ 'max-sm:hidden': !isFullScreen }"
					>
						<CarouselNext
							class="top-6"
						/>
						<CarouselPrevious
							class="top-6"
						/>
					</div>
					<div
						class="w-full gap-2 py-6 justify-center items-center sm:hidden"
						:class="{ 'xs:flex': !isFullScreen }"
					>
						<div
							v-for="(image, index) in images"
							:key="`scroll-image-${image.id}`"
							class="flex p-1 rounded-full bg-gray-300 h-1 transition ease-in-out duration-200"
							:data-index="index"
							:class="{ 'bg-primary-600 px-4': index === selectedIndex }"
							@click="onThumbClick(index)"
						/>
					</div>
				</div>
			</Carousel>
		</div>

		<Carousel
			class="relative w-full my-6"
			:class="{ 'max-sm:hidden': !isFullScreen }"
			@init-api="(val) => sliderThumbnailApi = val"
		>
			<CarouselContent class="mr-4 flex gap-4">
				<template v-if="loading">
					<Skeleton class="basis-1/4 h-20  bg-gray-200" />
				</template>
				<template v-else>
					<CarouselItem
						v-for="(image, index) in images"
						:key="image.id"
						dir="ltr"
						class="basis-1/5 cursor-pointer"
						:selected="selectedIndex"
						:class="{ 'sm:basis-1/12 sm:mx-2 xs:basis-1/6': isFullScreen, 'max-md:basis-1/4 max-md:me-2': !isFullScreen }"
						@click="onThumbClick(index)"
					>
						<div
							class="flex border border-gray-200 rounded-lg p-1 bg-white xs:h-14 xs:w-14 sm:w-20 sm:h-20"
							:class="{ 'border-2': isFullScreen, 'border-primary-600 !bg-gray-100': index === selectedIndex }"
						>
							<NuxtImg
								:src="image.preview"
								:alt="product.metaTitle"
								:title="product.metaTitle"
								class="object-contain"
								sizes="xs:100vw md:100px"
								width="100"
								height="100"
								format="webp"
								fit="contain"
								provider="backend"
								:loading="index<=4?'eager':'lazy'"
							/>
						</div>
					</CarouselItem>
				</template>
			</CarouselContent>
		</Carousel>
	</div>
</template>
