import Redis from 'ioredis'
// Alternative: import { createRedisStorage } from '~/server/utils/redis'

export default defineEventHandler(async (e) => {
	const config = useRuntimeConfig()
	const query = getQuery(e)

	// Allow db selection via query parameter, fallback to config, then default to 0
	const db = query.db ? parseInt(query.db as string, 10) : (config.redis.db ? config.redis.db : 0)

	try {
		// Use regular Redis connection instead of cluster since AWS ElastiCache is likely a single instance
		const redis = new Redis({
			host: config.redis.host,
			port: config.redis.port,
			db: db,
			tls: {
				servername: config.redis.host,
			},
		})

		console.log('Connected to Redis instance')

		const keys: string[] = [] // Get all keys in the selected database

		// Scan the single Redis instance directly
		console.log('Scanning Redis instance...')
		let cursor = '0'
		do {
			const [nextCursor, foundKeys] = await redis.scan(cursor, 'MATCH', '*', 'COUNT', 100)
			cursor = nextCursor
			console.log(`Scan result: cursor=${nextCursor}, foundKeys=${foundKeys.length}`)

			if (foundKeys.length > 0) {
				console.log(`Found keys:`, foundKeys)
				keys.push(...foundKeys)
			}
		} while (cursor !== '0')

		// console.log(`Total keys found: ${keys.length}`)

		// Close the connection
		await redis.quit()

		return {
			message: 'Cache keys',
			config: { ...config.redis, selectedDb: db },
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	}
})
