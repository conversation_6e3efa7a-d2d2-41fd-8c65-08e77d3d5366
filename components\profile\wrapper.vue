<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import type { User } from '~/interfaces/auth/auth'
import { useAuthStore } from '~/store/useAuthStore.client'

const isDesktop = useMediaQuery('(min-width: 600px)')
const authStore = useAuthStore()
const route = useRoute()
const userData = ref(null)
const { t } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	const { buildSinglePage } = useBreadcrumbs()
	return buildSinglePage(t('profile.page-title'))
})

watch(() => authStore.userSession, () => {
	userData.value = authStore.user as User
}, { immediate: true, deep: true })

const title = computed<string>(() => (route?.meta?.title || '') as string)
const isAwaiting = computed(() => authStore.isAwaiting && route.meta.id === 'account')

onMounted(() => {
	authStore.fetchUser()
})
</script>

<template>
	<div
		v-if="userData"
		class="flex flex-col w-full gap-6"
	>
		<Breadcrumb
			v-if="isDesktop"
			:links="breadCrumbLinks"
		/>

		<NuxtLinkLocale
			v-if="!isDesktop && title"
			to="/my/navigation"
			class="flex gap-4 py-6 px-4 items-center rounded-lg sm:hidden bg-white"
		>
			<Icon
				name="lucide:chevron-right"
				class="text-xl"
			/>
			<span class="text-lg font-bold">{{ $t(title) }}</span>
			<Badge
				v-if="isAwaiting"
				class="bg-yellow-600 text-2xs text-white drop-shadow-md ms-2"
			>
				{{ $t('form.is-awaiting') }}
			</Badge>
		</NuxtLinkLocale>

		<div class="flex w-full gap-6">
			<div
				v-if="isDesktop"
				class="sm:w-1/3 xs:w-full h-full"
			>
				<ProfileNavigation :user="userData" />
			</div>
			<slot v-bind="{ user: userData }" />
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
