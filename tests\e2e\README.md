# E2E Testing with <PERSON>wright

This project uses [Playwright](https://playwright.dev/) for end-to-end testing.

## Setup

Playwright is already installed as a dev dependency. To set up the browsers:

```bash
npm run test:e2e:install
```

## Running Tests

### All Tests
```bash
npm run test:e2e
```

### Interactive UI Mode
```bash
npm run test:e2e:ui
```

### Debug Mode
```bash
npm run test:e2e:debug
```

### Headed Mode (see browser)
```bash
npm run test:e2e:headed
```

### Specific Browsers
```bash
npm run test:e2e:chromium
npm run test:e2e:firefox
npm run test:e2e:webkit
npm run test:e2e:mobile
```

## Test Structure

```
tests/e2e/
├── helpers/
│   └── test-helpers.ts    # Common test utilities and page objects
├── auth-login.spec.ts     # Login functionality tests
└── basic.spec.ts          # Basic app functionality tests
```

## Test Configuration

- **Configuration**: `playwright.config.ts`
- **Test Environment**: `.env.test`
- **Reports**: Generated in `playwright-report/`

## Login Test Example

The login test (`auth-login.spec.ts`) demonstrates:

- ✅ Modal opening/closing
- ✅ Form validation
- ✅ Loading states
- ✅ Error handling
- ✅ Navigation between auth flows
- ✅ Accessibility testing
- ✅ Mobile responsiveness

### Test Categories

1. **Login Modal**: Basic modal functionality
2. **Form Validation**: Input validation and error states
3. **Login Functionality**: Authentication flow
4. **UI Interactions**: Phone input, password toggle
5. **Accessibility**: Keyboard navigation, ARIA attributes

## Environment Setup

### Test Data

Update `.env.test` with your test environment settings:

```env
PLAYWRIGHT_BASE_URL=http://localhost:3000
TEST_USER_PHONE=+************
TEST_USER_PASSWORD=TestPassword123
```

### Valid Login Test

The test `should successfully login with valid credentials` is currently skipped. To enable it:

1. Create test user accounts in your test environment
2. Update `TestData.validCredentials` in `test-helpers.ts`
3. Remove `.skip()` from the test

## Page Object Pattern

The tests use the Page Object pattern with helper classes:

- `AuthHelpers`: Login/authentication-related interactions
- `AppHelpers`: General app navigation and common elements
- `TestData`: Test data constants

## CI/CD Integration

The configuration is ready for CI/CD:

- Uses different settings for CI vs local development
- Generates multiple report formats (HTML, JSON, JUnit)
- Retries failed tests in CI environment
- Captures screenshots and videos on failure

## Best Practices

1. **Stable Selectors**: Use data-testid attributes for reliable element selection
2. **Wait Strategies**: Use proper wait conditions instead of fixed timeouts
3. **Page Objects**: Keep test logic organized in helper classes
4. **Test Isolation**: Each test should be independent
5. **Descriptive Names**: Use clear test descriptions

## Debugging

1. **Debug Mode**: Use `npm run test:e2e:debug` to step through tests
2. **Screenshots**: Check `playwright-report/` for failure screenshots
3. **Trace Viewer**: Analyze test traces for detailed debugging
4. **Console Logs**: Tests capture and display console errors

## Adding New Tests

1. Create test files with `.spec.ts` suffix in `tests/e2e/`
2. Use the helper classes for common operations
3. Follow the existing test structure and naming conventions
4. Add test data to `TestData` object when needed

## Mobile Testing

Tests include mobile device simulation:
- iPhone 12 (Mobile Safari)
- Pixel 5 (Mobile Chrome)

Run mobile-specific tests:
```bash
npm run test:e2e:mobile
```
