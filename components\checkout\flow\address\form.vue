<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { Address, CityResponse } from '~/interfaces/auth/address'
import type FormPhoneValue from '~/interfaces/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const emit = defineEmits<{
	(event: 'address:success', addressId: number): void
	(event: 'close:address'): void
}>()

const { address = null } = defineProps<{
	address?: Address | null
}>()

const { t } = useI18n()
const authStore = useAuthStore()
const { data } = await useApi<CityResponse[]>('lookups-website/cities')
const cites = computed(() => data.value as CityResponse[])
const form = ref({ ...address })
const loading = ref<boolean>(false)
const isEdit = computed(() => !!address?.addressId)
const isUserLoggedIn = computed(() => authStore.isLoggedIn)

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			cityId: z.number()
				.min(1, t('error.required'))
				.nullish()
				.refine(val => val !== null && val !== undefined, {
					message: t('error.required'),
				}),
			district: z.string().min(1, t('error.required')),
			fullAddress: z.string().optional(),
			buildingType: z.string().min(1, t('error.required')),
			default: z.union([z.boolean(), z.number()]).optional(),
			isProfileDataShared: z.union([z.boolean(), z.number()]).optional(),
			firstName: z.string().nullable().optional(),
			lastName: z.string().nullable().optional(),
			phone: z.object({
				number: z.string().nullable().optional(),
				iso: z.string().nullable().optional(),
				code: z.string().nullable().optional(),
				isValid: z.boolean().optional(),
			}).nullable().optional(),
		}).superRefine((data, ctx) => {
			if (isUserLoggedIn.value && data.isProfileDataShared) {
				data.firstName = undefined
				data.lastName = undefined
				data.phone = undefined
				return data
			}

			if (!data.firstName || data.firstName.trim() === '') {
				ctx.addIssue({
					path: ['firstName'],
					code: z.ZodIssueCode.custom,
					message: t('error.required'),
				})
			}
			if (!data.lastName || data.lastName.trim() === '') {
				ctx.addIssue({
					path: ['lastName'],
					code: z.ZodIssueCode.custom,
					message: t('error.required'),
				})
			}
			if (!data.phone?.isValid) {
				ctx.addIssue({
					path: ['phone'],
					code: z.ZodIssueCode.custom,
					message: t('error.phone-number-invalid'),
				})
			}
			return ctx
		}),
	),
	initialValues: toRaw({
		...form.value,
	}),
	validateOnMount: false,
})

const isProfileDataShared = computed<boolean>({
	get: () => !!Form.values.isProfileDataShared as boolean,
	set: (val: boolean) => {
		Form.setFieldValue('isProfileDataShared', val)
	},
})

/** handle on submit the form **/
const onSubmitChange = Form.handleSubmit(async (payload) => {
	loading.value = true
	const { $api } = useNuxtApp()
	await $api<Address>(`addresses`, {
		method: isEdit.value ? 'PUT' : 'POST',
		body: payload,
	})
		.then((data) => {
			toast.success(
				isEdit.value
					? t('form.address-updated-success')
					: t('form.address-added-success'),
			)
			emit('address:success', data.addressId)
			nextTick(() => {
				Form.resetForm()
			})
		})
		.finally(() => {
			loading.value = false
		})
})
</script>

<template>
	<div class="flex gap-2 px-4 w-full min-w-3xl mb-2 max-sm:flex-col">
		<div class="grid grid-cols-2 gap-6 col-span-1 sm:w-1/2 xs:w-full">
			<span class="text-lg col-span-2 font-bold">{{ $t('form.address-modal-title') }}</span>

			<FormField
				v-slot="{ componentField }"
				name="cityId"
				class="col-span-1"
			>
				<FormItem class="w-full flex flex-col">
					<FormLabel class="font-semibold">
						{{ $t('form.city') }}*
					</FormLabel>
					<FormControl>
						<select
							id="city"
							v-model.number="Form.values.cityId"
							v-bind="componentField"
							class="text-sm !outline-none border border-gray-200 rounded px-2 py-1 h-11"
						>
							<option :value="null">
								{{ $t('form.select-city') }}
							</option>

							<option
								v-for="city in cites"
								:key="`city_id_${city.value}`"
								:value="city.value"
							>
								{{ city.text }}
							</option>
						</select>
					</FormControl>
					<FormMessage />
				</FormItem>
			</FormField>

			<FormField
				v-slot="{ componentField }"
				name="district"
				class="col-span-1"
			>
				<FormItem class="w-full flex flex-col">
					<FormLabel class="font-semibold">
						{{ $t('form.area') }}*
					</FormLabel>
					<FormControl>
						<Input
							class="h-11"
							type="text"
							:placeholder="$t('form.area')"
							v-bind="componentField"
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			</FormField>

			<FormField
				v-slot="{ componentField }"
				name="fullAddress"
			>
				<FormItem class="w-full flex flex-col col-span-2">
					<FormLabel class="font-semibold">
						{{ $t('form.full-address') }}*
					</FormLabel>
					<FormControl>
						<Input
							class="h-11"
							type="text"
							:placeholder="$t('form.full-address')"
							v-bind="componentField"
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			</FormField>

			<div class="flex flex-col col-span-2 gap-2">
				<div class="flex gap-2 items-center">
					<span class="text-sm font-semibold">{{ $t('form.receipt-details') }}</span>
					<span class="text-gray-500 text-sm">({{ $t('form.optional') }})</span>
				</div>
				<div class="flex gap-4 items-center cursor-pointer mt-2">
					<div class="flex gap-2 items-center">
						<input
							id="house"
							v-model="Form.values.buildingType"
							type="radio"
							name="location"
							value="home"
							class="w-4 h-4"
						>

						<label
							for="house"
							class="text-sm"
						>
							{{ $t('form.house') }}
						</label>
					</div>
					<div class="flex gap-2 items-center">
						<input
							id="work"
							v-model="Form.values.buildingType"
							type="radio"
							name="location"
							value="work"
							class="w-4 h-4"
						>

						<label
							for="work"
							class="text-sm"
						>
							{{ $t('form.work') }}
						</label>
					</div>
				</div>
			</div>
		</div>
		<div
			class="flex w-px bg-gray-100 flex-grow mx-2 min-h-full p-px shadow-sm max-sm:min-w-full max-sm:min-h-auto max-sm:my-4"
		/>
		<div class="grid grid-cols-2 gap-6 col-span-1 sm:w-1/2 xs:w-full">
			<div class="flex w-full col-span-2 justify-between items-start max-sm:flex-col max-sm:items-start">
				<span class="text-lg font-bold text-nowrap">{{ $t('form.receipt-details') }}</span>

				<div
					v-if="isUserLoggedIn"
					class="flex items-center gap-2 w-full justify-end max-sm:justify-start "
				>
					<label
						:for="`is-shared-${address?.addressId}`"
						class="font-semibold text-xs"
					>
						{{ $t('form.use-user-address-details') }}
					</label>
					<Switch
						:id="`is-shared-${address?.addressId}`"
						v-model="isProfileDataShared"
					/>
				</div>
			</div>

			<template v-if="!isProfileDataShared">
				<FormPhone
					:error="Form.errors?.value?.phone"
					@update="(value: FormPhoneValue) => {
						Form.setFieldValue('phone', {
							number: value.nationalNumber,
							code: value.countryCallingCode,
							iso: value.countryCode,
							isValid: value.isValid,
						})
					}"
				/>

				<FormField
					v-slot="{ componentField }"
					name="firstName"
				>
					<FormItem class="w-full flex flex-col col-span-1">
						<FormLabel class="font-semibold">
							{{ $t('form.first-name') }}
						</FormLabel>
						<FormControl>
							<Input
								class="h-11"
								type="text"
								:placeholder="$t('form.first-name')"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<FormField
					v-slot="{ componentField }"
					name="lastName"
				>
					<FormItem class="w-full flex flex-col col-span-1">
						<FormLabel class="font-semibold">
							{{ $t('form.last-name') }}
						</FormLabel>
						<FormControl>
							<Input
								class="h-11"
								type="text"
								:placeholder="$t('form.last-name')"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>
			</template>
		</div>
	</div>

	<div class="flex justify-end gap-4 w-full my-2 max-sm:my-8 max-sm:justify-between items-center py-4">
		<Button
			variant="outline"
			class="max-sm:w-full"
			@click.prevent="() => emit('close:address')"
		>
			{{ $t('form.cancel') }}
		</Button>

		<Button
			class="max-sm:w-full"
			:loading="loading"
			@click="() => onSubmitChange()"
		>
			<span v-if="isEdit">{{ $t('form.edit-address') }}</span>
			<span v-else>{{ $t('form.save-address') }}</span>
		</Button>
	</div>
</template>
