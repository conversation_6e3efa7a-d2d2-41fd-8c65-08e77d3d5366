export interface Rate {
	ratings: Rating[]
	avgRating: number
}

export interface Rating {
	totalReviews: number
	reviews: Review[]
	percentage: number
	rating: number
}

export interface Review {
	review: string
	ratingId: number
	userId: number
	user: User
	status: string
	rating?: number
}

export interface User {
	firstName: string
	lastName: string
	media: Media
	userId: number
}

export interface Media {
	avatar?: Avatar
}

export interface Avatar {
	preview: string
	disk: string
	src: string
	fileSize: number
	id: number
	mimeType: string
	sort: number
}
