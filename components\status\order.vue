<script setup lang="ts">
const { status } = defineProps<{
	status?: string
}>()

// draft canceled completed refunded received
</script>

<template>
	<Badge :class="`order-status ${status}`">
		{{ $t(`orders.${status}`) }}
	</Badge>
</template>

<style scoped lang="scss">
.order-status {
  @apply max-sm:scale-75 max-sm:text-sm whitespace-nowrap;
  &.draft {
    @apply bg-orange-400 text-white;
  }
  &.received {
    @apply bg-blue-500 text-white;
  }

  &.completed {
    @apply bg-green-1000 text-white;
  }

  &.canceled {
    @apply bg-rose-600 text-white;
  }

  &.refunded {
    @apply bg-rose-400 text-white;
  }

}
</style>
