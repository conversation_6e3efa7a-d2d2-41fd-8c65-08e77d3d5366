<script setup lang="ts">
const { t } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('services.title'))
})

const list = ref([
	{
		title: t('services.service-1-title'),
		text: t('services.service-1-text'),
		icon: 'ui:service-smartphone',
	},

	{
		title: t('services.service-2-title'),
		text: t('services.service-2-text'),
		icon: 'ui:service-visa',
	},

	{
		title: t('services.service-3-title'),
		text: t('services.service-3-text'),
		icon: 'ui:service-fix',
	},

	{
		title: t('services.service-4-title'),
		text: t('services.service-4-text'),
		icon: 'ui:service-care',
	},

	{
		title: t('services.service-5-title'),
		text: t('services.service-5-text'),
		icon: 'ui:service-support',
	},

	{
		title: t('services.service-6-title'),
		text: t('services.service-6-text'),
		icon: 'ui:service-tag',
	},
])

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)

useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})

useSeoMeta({
	title: () => `${t('services.meta-title')}`,
	description: () => t('services.meta-description'),
	ogTitle: () => `${t('services.meta-title')}`,
	ogDescription: () => t('services.meta-description'),
	twitterTitle: () => `${t('services.meta-title')}`,
	twitterDescription: () => t('services.meta-description'),
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<Card class="flex flex-col w-full h-full gap-2 my-6">
			<CardHeader class="relative text-center justify-center gap-4 text-white  rounded-lg !p-0">
				<NuxtImg
					src="/images/services/lg.png"
					sizes="1765px"
					width="1765"
					height="537"
					format="webp"
					quality="90"
					class="rounded-lg object-cover hidden lg:block w-full"
					alt="Large screen image"
					:preload="true"
				/>

				<NuxtImg
					src="/images/services/md.png"
					sizes="991px"
					width="991"
					height="296"
					format="webp"
					quality="90"
					class="rounded-lg object-cover hidden md:block lg:hidden w-full"
					alt="Medium screen image"
					:preload="true"
				/>

				<NuxtImg
					src="/images/services/sm.png"
					sizes="398px"
					width="398"
					height="296"
					format="webp"
					quality="90"
					class="rounded-lg object-cover block md:hidden w-full"
					alt="Small screen image"
					:preload="true"
				/>
				<div class="absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0">
					<h1 class="font-bold text-2xl max-sm:text-sm">
						{{ $t('services.title') }}
					</h1>
					<p class="text-xl font-semibold max-sm:text-xs">
						{{ $t('services.sub-title') }}
					</p>
				</div>
			</CardHeader>
			<CardContent class="grid grid-cols-3 max-sm:grid-cols-1 gap-6 py-12">
				<div
					v-for="(item, index) in list"
					:key="index"
					class="flex flex-col p-4 rounded-lg shadow gap-2 border"
				>
					<div class="flex justify-between items-center">
						<h2 class="text-base font-semibold">
							{{ item.title }}
						</h2>
						<Icon
							:name="item.icon"
							size="25px"
						/>
					</div>
					<span class="text-sm text-gray-600">{{ item.text }}</span>
				</div>
			</CardContent>
		</Card>
	</card>
</template>
