<script setup lang="ts">
import { Button } from '~/components/ui/button'
import { useAuthStore } from '~/store/useAuthStore.client'
import { useCompareStore } from '~/store/useCompareStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'
import { useCartStore } from '~/store/useCartStore'

const emit = defineEmits<{
	(event: 'open:drawer', page: string): void
}>()

const { hasScroll } = defineProps<{
	hasScroll: boolean
}>()

const router = useRouter()

const favoriteStore = useFavoriteStore()
const cartStore = useCartStore()
const authStore = useAuthStore()
const compareStore = useCompareStore()
const localePath = useLocalePath()

const wishListCount = computed(() => favoriteStore.count || 0)
const cartListCount = computed(() => cartStore.count || 0)
const compareListCount = computed(() => compareStore.count || 0)
const isUserLoggedIn = computed(() => !!authStore.isLoggedIn)

const openComparePage = () => {
	if (compareListCount.value <= 1) {
		return false
	}

	const ids = compareStore.products.join('/')
	router.push(localePath(`/compare/${ids}`))
}

onMounted(() => {
	nextTick(() => {
		compareStore.fetchProducts()
	})
})
</script>

<template>
	<div
		class="max-sm:hidden container flex items-center w-full justify-between h-20 my-2 py-2 gap-x-4"
	>
		<NuxtLinkLocale
			to="/"
			class="flex items-center w-28"
		>
			<NuxtImg
				class="max-h-16 app-logo mx-auto aspect-auto"
				:class="{ 'w-20': hasScroll }"
				:alt="$t('app.action-mobile-logo')"
				:title="$t('app.action-mobile-logo')"
				src="/images/logo.png"
				:width="100"
				:height="53"
				:sizes="{
					sm: 70,
					md: 100,
				}"
				format="webp"
				loading="eager"
				:preload="true"
			/>
		</NuxtLinkLocale>

		<AppHeaderSearch />

		<div class="grid grid-cols-5 gap-4">
			<!--			<Button variant="icon"> -->
			<!--				<Icon -->
			<!--					name="lucide:bell" -->
			<!--					size="20px" -->
			<!--				/> -->
			<!--			</Button> -->

			<Button
				variant="icon"
				class="relative"
				:class="{ 'bg-primary-300 !text-primary-600': !!compareListCount }"
				@click="openComparePage"
			>
				<Icon
					name="ui:compare"
					size="20px"
				/>
				<Badge
					class="absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none"
					:class="{ hidden: !compareListCount }"
				>
					<span class="text-2xs font-semibold text-white leading-none">{{ compareListCount }}</span>
				</Badge>
			</Button>

			<Button
				variant="icon"
				:as-child="true"
				:class="{ 'bg-primary-300 !text-primary-600': isUserLoggedIn }"
			>
				<NuxtLinkLocale
					:to="isUserLoggedIn?'/my/profile':'/?auth=login'"
					class="flex justify-center items-center"
				>
					<Icon
						name="lucide:circle-user-round"
						size="20px"
					/>
				</NuxtLinkLocale>
			</Button>
			<Button
				variant="icon"
				class="relative"
				:class="{ 'bg-primary-300 !text-primary-600': !!wishListCount }"
				@click="() => emit('open:drawer', 'wishlist')"
			>
				<Icon
					name="lucide:heart"
					size="20px"
				/>
				<Badge
					class="absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none"
					:class="{ hidden: !wishListCount }"
				>
					<span class="text-2xs font-semibold text-white leading-none">{{ wishListCount }}</span>
				</Badge>
			</Button>

			<Button
				variant="icon"
				class="relative"
				:class="{ 'bg-primary-300 !text-primary-600': cartListCount }"
				@click="() => emit('open:drawer', 'cart')"
			>
				<Icon
					name="lucide:shopping-cart"
					size="20px"
				/>
				<Badge
					class="absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none"
					:class="{ hidden: !cartListCount }"
				>
					<span class="text-2xs font-semibold text-white leading-none">{{ cartListCount }}</span>
				</Badge>
			</Button>
		</div>
	</div>
</template>
