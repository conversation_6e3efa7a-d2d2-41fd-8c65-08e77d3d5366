export interface OrderResponse {
	items?: Order[]
	pagination?: Pagination
}

export interface Pagination {
	total?: number
	count?: number
	perPage?: number
	page?: number
	lastPage?: number
}

export interface Order {
	orderId?: number
	status?: string
	paymentStatus?: string
	paymentMethodId?: number
	createdAt?: string
	updatedAt?: string
	userId?: number
	addressId?: number
	total?: Total
	subTotal?: SubTotal
	shippingPrice?: ShippingPrice
	tax?: number
	visitorId?: number
	shippingCarrierId?: number
	deletedAt?: string
	user?: User
	address?: Address
	orderItems?: OrderItem[]
	shippingCarrier?: ShippingCarrier
	paymentMethod?: PaymentMethod
}

export interface Total {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface SubTotal {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface ShippingPrice {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface User {
	userId?: number
	firstName?: string
	lastName?: string
	email?: string
	emailVerifiedAt?: string
	birthday?: string
	status?: string
	createdAt?: string
	updatedAt?: string
	phone?: Phone
	gender?: string
	fullName?: string
	wallet?: Wallet
	media?: Media
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
}

export interface Wallet {
	value?: number
	currency?: string
	symbol?: string
}

export interface Media {
	mediaId?: number
	src?: string
}

export interface Address {
	addressId?: number
	cityId?: number
	district?: string
	createdAt?: string
	updatedAt?: string
	userId?: number
	phone?: Phone
	street?: string
	recipientName?: string
	apartmentNumber?: string
	buildingNumber?: string
	default?: number
	visitorId?: number
	firstName?: string
	lastName?: string
	email?: string
	buildingType?: string
	fullAddress?: string
}

export interface ShippingCarrier {
	shippingCarrierId?: number
	name?: string
	slug?: string
	phone?: Phone
	label?: string
	haveFastShipping?: boolean
	default?: boolean
	createdAt?: string
	updatedAt?: string
}

export interface PaymentMethod {
	paymentMethodId?: number
	name?: string
	module?: string
	createdAt?: string
	updatedAt?: string
	status?: string
	allowFillWallet?: number
}

export interface OrderItem {
	orderItemsId?: number
	orderId?: number
	model_type?: string
	model_id?: number
	snapshot?: Snapshot
	productOrderItemId?: number
	productId?: number
	varianceId?: number
	brandId?: number
	quantity?: number
	status?: string
	discount?: number
	stockId?: number
	product?: Product
	variance?: Variance
	price?: Price
	originalPrice?: OriginalPrice
	order?: Order
}

export interface Snapshot {
	product?: Product
	variance?: Variance
	activeStock?: ActiveStock
}

export interface Product {
	name?: string
	slug?: string
	type?: string
	colors?: string
	avgRate?: number
	brandId?: number
	oldSlug?: string
	hasOffer?: boolean
	hasStock?: boolean
	isListed?: boolean
	maxPrice?: number
	minPrice?: number
	priority?: number
	createdAt?: string
	deletedAt?: string
	metaTitle?: string
	productId?: number
	releaseAt?: string
	updatedAt?: string
	shippingId?: string
	description?: string
	isPublished?: boolean
	publishedAt?: string
	numberOfOrder?: number
	unPublishedAt?: string
	productGroupId?: string
	metaDescription?: string
	maxPriceBeforeOffer?: number
	minPriceBeforeOffer?: number
	variationAttributes?: string
}

export interface Variance {
	SKU?: string
	name?: string
	slug?: string
	type?: string
	brandId?: number
	oldSlug?: string
	auctionId?: number
	createdAt?: string
	deletedAt?: string
	isDefault?: boolean
	metaTitle?: string
	productId?: number
	updatedAt?: string
	varianceId?: number
	activeStock?: ActiveStock
	isPublished?: number
	modelNumber?: string
	publishedAt?: string
	unPublishedAt?: string
	metaDescription?: string
}

export interface ActiveStock {
	cost?: number
	note?: string
	sold?: number
	sort?: number
	price?: number
	isOffer?: boolean
	stockId?: number
	quantity?: number
	createdAt?: string
	updatedAt?: string
	isPreOrder?: boolean
	maxPerUser?: number
	supplierId?: number
	isPublished?: boolean
	publishedAt?: string
	unPublishedAt?: string
	priceBeforeOffer?: number
	laravel_through_key?: number
}

export interface Product {
	productId?: number
	name?: string
	brandId?: number
	productGroupId?: string
	description?: string
	type?: string
	slug?: string
	isListed?: boolean
	variationAttributes?: string
	avgRate?: number
	media?: Media
}

export interface Media {
	gallery?: Gallery[]
	cover?: Cover[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: string
}

export interface Cover {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: string
}

export interface Variance {
	varianceId?: number
	auctionId?: number
	name?: string
	slug?: string
	brandId?: number
	SKU?: string
	type?: string
}

export interface Price {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface OriginalPrice {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}
