# Action Mobile App

This is a Nuxt.js-based mobile application with Capacitor integration for native mobile functionality.

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v18 or later recommended)
- [npm](https://www.npmjs.com/) (v8 or later)
- [Git](https://git-scm.com/)
- [Java Development Kit (JDK) 21](https://www.oracle.com/java/technologies/downloads/#java21) (for Android development)
- [Android Studio](https://developer.android.com/studio) (for Android development)
- [Xcode](https://developer.apple.com/xcode/) (for iOS development, macOS only)

## Setting Up the Development Environment

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/action.v2-frontend.git
cd action.v2-frontend
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Create a `.env` file in the root directory with the following content:

```
BASE_URL=https://stg.action.jo/api/v1/
NODE_ENV=development
ENABLE_DEBUG=true
ENABLE_CACHE=false
```

For Capacitor builds, we also have a `.env.capacitor` file:

```
NODE_ENV=capacitor
BASE_URL=https://stg.action.jo/api/v1/
ENABLE_DEBUG=false
ENABLE_CACHE=true
```

### 4. Configure Java for Android Development

Ensure JAVA_HOME is set to JDK 21:

#### Windows:
```bash
setx JAVA_HOME "C:\Program Files\Java\jdk-21"
setx PATH "%PATH%;%JAVA_HOME%\bin"
```

#### macOS/Linux:
```bash
export JAVA_HOME=/path/to/jdk-21
export PATH=$PATH:$JAVA_HOME/bin
```

Verify with:
```bash
java -version
```

### 5. Configure Node.js Path

Ensure Node.js is in your PATH:

#### Windows:
```bash
setx PATH "%PATH%;C:\Program Files\nodejs"
```

#### macOS/Linux:
```bash
export PATH=$PATH:/usr/local/bin/node
```

Verify with:
```bash
node -v
npm -v
```

## Running the Application

### Web Development

To start the development server:

```bash
npm run dev
```

This will start the Nuxt.js development server, typically at http://localhost:3000.

### Building for Production (Web)

```bash
npm run build
npm run preview
```

### Generating Static Site

```bash
npm run generate
```

### Capacitor Development

#### Initial Setup

```bash
npm run generate:capacitor
npx cap sync
```

#### Running on Android

```bash
npm run cap:android
```

Or step by step:
```bash
npm run generate:capacitor
npx cap sync
npx cap run android
```

#### Running on iOS (macOS only)

```bash
npm run cap:ios
```

Or step by step:
```bash
npm run generate:capacitor
npx cap sync
npx cap run ios
```

## Project Structure

- `assets/` - Static assets like CSS, images, and fonts
- `components/` - Vue components
- `composables/` - Vue composables
- `layouts/` - Page layouts
- `pages/` - Application pages and routes
- `plugins/` - Nuxt.js plugins
- `public/` - Public static files
- `android/` - Android platform files (Capacitor)
- `ios/` - iOS platform files (Capacitor)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run generate` - Generate static site
- `npm run generate:capacitor` - Generate with Capacitor environment
- `npm run preview` - Preview production build
- `npm run lint` - Run linter
- `npm run lint:fix` - Fix linting issues
- `npm run cap:sync` - Sync Capacitor
- `npm run cap:build` - Build for Capacitor
- `npm run cap:android` - Build and run on Android
- `npm run cap:ios` - Build and run on iOS

## Troubleshooting

### Java Version Issues

If you encounter Java version errors when running Android:

```
error: invalid source release: 21
```

Make sure you have JDK 21 installed and JAVA_HOME is correctly set.

### Node.js Not Found

If you get errors about `node` or `npm` not being found:

1. Verify Node.js is installed: `node -v`
2. Make sure it's in your PATH
3. Try using the full path: `C:\Program Files\nodejs\npm.cmd`

### Capacitor Build Issues

If Capacitor builds fail:

1. Make sure you've run `npm run generate:capacitor` first
2. Verify Android Studio/Xcode is properly installed
3. Check that the Android/iOS platform has been added: `npx cap add android` or `npx cap add ios`

## Additional Resources

- [Nuxt.js Documentation](https://nuxt.com/docs)
- [Capacitor Documentation](https://capacitorjs.com/docs)
- [Vue.js Documentation](https://vuejs.org/guide/introduction.html)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
