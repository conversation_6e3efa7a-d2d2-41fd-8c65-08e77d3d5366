<script setup lang="ts">
import type { Items } from '~/interfaces/cart/cart-list'
import { useCartStore } from '~/store/useCartStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'

interface Payload {
	quantity?: number
	productId?: number
	varianceId?: number
	bundleId?: unknown
}

const emit = defineEmits<{
	(event: 'add:cart-list'): void
}>()

const listLength = computed(() => list.value?.length as number)
const isEmptyList = computed(() => !listLength.value as boolean)

const cartStore = useCartStore()
const favoriteStore = useFavoriteStore()
const list = computed(() => (cartStore?.list || []) as Items[])
const selectedRemoveCartId = ref(null)
const isRemoving = ref<boolean>(false)
const selectedProduct = computed(() => list.value.find(i => i.cartId === selectedRemoveCartId.value))
/**
 * Handles the removal of a cart from the list in the cart.
 *
 * @returns {Promise<void>} - A promise that resolves when the cart is successfully removed from the list.
 */
const onRemoveFromList = async (): Promise<void> => {
	isRemoving.value = true
	await cartStore.removeFromList(selectedRemoveCartId.value)
	selectedRemoveCartId.value = null
	return nextTick(() => isRemoving.value = false)
}

/**
 * Asynchronous function that adds a selected product to the favorite list and then
 * removes it from another list.
 * This function interacts with `favoriteStore` to include the selected product
 * in a wish list and calls `onRemoveFromList` to manage the removal process.
 *
 * Dependencies:
 * - Expects `favoriteStore` to have an `addToList` method.
 * - Requires `selectedProduct` to be an observable containing `productId`.
 * - Relies on the `onRemoveFromList` function to complete the removal.
 *
 * Use this function to handle the addition of selected products to a wish list
 * and manage previous list removals asynchronously.
 */
const onWishList = async () => {
	await favoriteStore.addToList(selectedProduct.value.productId)
	await onRemoveFromList()
}

/**
 * Asynchronously updates the quantity of an item in the cart.
 *
 * @function
 * @async
 * @param {Object} payload - The data required to update the quantity, typically containing item-specific information and the desired quantity.
 * @returns {Promise<void>} A promise that resolves when the update operation is complete.
 */
const updateQuantity = async (payload: Payload): Promise<void> => {
	await cartStore.updateQuantity(payload)
}
</script>

<template>
	<div class="flex flex-col w-full  h-full text-gray-700 overflow-y-auto max-h-full">
		<template v-if="isEmptyList">
			<div class="flex flex-col w-full h-full items-center gap-6">
				<Icon
					name="ui:empty-cart-list"
					class="w-full h-60 mt-20"
				/>
				<div class="flex text-lg max-w-xs text-center font-semibold">
					{{ $t('cart-list.empty-text') }}
				</div>

				<Button
					variant="default"
					class="w-1/2"
					@click="() => emit('add:cart-list')"
				>
					{{ $t('cart-list.empty-button') }}
				</Button>
			</div>
		</template>
		<template v-else>
			<div class="flex flex-col max-w-full gap-6 p-4">
				<DrawerCartListCard
					v-for="item in list"
					:key="`cart-list-item-${item.productId}`"
					:product="item"
					@remove:cart="cartId => selectedRemoveCartId = cartId"
					@update:quantity="updateQuantity"
				/>
			</div>
		</template>
	</div>

	<Modal
		v-if="!!selectedRemoveCartId"
		:title="$t('cart-list.remove-item-title')"
		:description="$t('cart-list.remove-item-description')"
		@close="selectedRemoveCartId = null"
	>
		<template #footer>
			<div class="flex w-full justify-between items-center">
				<Button
					variant="outline"
					@click.prevent="selectedRemoveCartId = null"
				>
					<span>{{ $t('form.cancel') }}</span>
				</Button>

				<div class="flex gap-2 items-center">
					<Button
						variant="danger"
						:loading="isRemoving"
						@click.once="onRemoveFromList"
					>
						<span>{{ $t('form.remove') }}</span>
					</Button>

					<Button @click.once="onWishList">
						<span>{{ $t('form.move-to-wish-list-btn') }}</span>
					</Button>
				</div>
			</div>
		</template>

		<template #body>
			<div class="flex w-full px-4 justify-center items-center mb-4">
				<DrawerCartListCard
					v-if="!!selectedProduct"
					:product="selectedProduct"
					:view-only="true"
				/>
			</div>
		</template>
	</Modal>
</template>
