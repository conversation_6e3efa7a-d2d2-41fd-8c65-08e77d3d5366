<script lang="ts" setup>
import PhoneInput from 'base-vue-phone-input'
import { useFocus } from '@vueuse/core'
import { FormPhoneFlags } from '#components'
import type { AuthPhone } from '~/interfaces/auth/form'
import type FormPhoneValue from '~/interfaces/form'

const open = ref(false)
const { locale } = useI18n()
const phoneInput = ref(null)
const { focused } = useFocus(phoneInput)
const isReady = ref(false)
const { title, error = '', value } = defineProps<{
	title?: string
	error?: string
	value?: AuthPhone
}>()

const emit = defineEmits<{
	(event: 'update', value: FormPhoneValue): void
}>()

const phoneValue = ref(`${value?.number}`)
const number = ref(value?.number)
const isRtl = computed(() => locale.value === 'ar')

const onUpdatePhone = (phone: FormPhoneValue): void => {
	if (!isReady.value) {
		return
	}
	emit('update', phone)
}

onMounted(() => {
	isReady.value = true
})
</script>

<template>
	<div
		class="relative col-span-2 flex flex-col w-full gap-2"
	>
		<label
			for="phone"
			class="text-sm font-semibold"
			:class="{ 'text-destructive': !!error }"
		>
			{{ title || $t('form.phone') }}*
		</label>
		<PhoneInput
			id="phone"
			v-model="phoneValue"
			no-use-browser-locale
			fetch-country
			:dir="'rtl'"
			class="flex flex-row-reverse w-full border rounded-md px-2 items-center text-sm h-11"
			:ignored-countries="['AC']"
			:country-code="value?.iso"
			:country-locale="isRtl?'ar-JO':'en-US'"
			:auto-format="true"
			:translations="{
				countrySelector: {
					placeholder: 'Country code',
					error: 'Choose country',
					searchPlaceholder: 'Search the country',
				},
				phoneInput: {
					placeholder: 'Phone number',
					example: 'Ex:',
				},
			}"
			@data="onUpdatePhone"
		>
			<template #input="{ inputValue, updateInputValue, placeholder }">
				<Input
					ref="phoneInput"
					:dir="'ltr'"
					class="border-none px-1"
					:class="{ 'text-end': isRtl }"
					type="text"
					:model-value="number || inputValue"
					:placeholder="placeholder"
					@input="updateInputValue"
				/>
			</template>

			<template #selector="{ inputValue, updateInputValue, countries }">
				<ClientOnly>
					<Popover
						v-model:open="open"
						class="p-0 gap-0"
					>
						<PopoverTrigger class="border-s">
							<button
								class="flex justify-center items-center gap-4 pe-1 ps-4 hover:bg-transparent h-11"
							>
								<FormPhoneFlags :country="inputValue" />
								<Icon
									name="lucide:chevrons-up-down"
									class="-mr-2 h-4 w-4 opacity-50"
								/>
							</button>
						</PopoverTrigger>
						<PopoverContent class="w-[300px] p-0 outline-none">
							<Command>
								<CommandInput :placeholder="$t('form.search-country')" />
								<CommandEmpty>{{ $t('form.no-country-found') }}</CommandEmpty>
								<CommandList>
									<CommandGroup>
										<CommandItem
											v-for="option in countries"
											:key="option.iso2"
											:value="option.name"
											class="gap-2 outline-none !bottom-none"
											@select="
												() => {
													updateInputValue(option.iso2)
													open = false
													focused = true
												}
											"
										>
											<FormPhoneFlags :country="option?.iso2" />
											<span class="flex-1 text-sm">
												{{ option.name }}
											</span>
											<span class="text-foreground/50 text-sm">
												{{ option.dialCode }}
											</span>
										</CommandItem>
									</CommandGroup>
								</CommandList>
							</Command>
						</PopoverContent>
					</Popover>
				</ClientOnly>
			</template>
		</PhoneInput>
		<span class="text-xs font-normal text-destructive">
			{{ error }}
		</span>
	</div>
</template>
