<script setup lang="ts">
import { toast } from 'vue-sonner'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import type { User } from '~/interfaces/auth/auth'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()

const { t } = useI18n()
const today = new Date()
const minAge = 12
const maxDate = new Date(today.getFullYear() - minAge, today.getMonth(), today.getDate())
const formattedMaxDate = maxDate.toISOString().split('T')[0] as string
const isSaving = ref<boolean>(false)
const editModal = ref<string>(null)

const AccountForm = useForm({
	validationSchema: toTypedSchema(z.object({
		email: z.string().email().optional(),
		firstName: z.string().min(1, t('error.required')),
		lastName: z.string().min(1, t('error.required')),
		birthday: z.any().optional(),
		gender: z.union([z.enum(['male', 'female']), z.null()]).optional(),
		phone: z.object({
			number: z.string().optional(),
			code: z.string().optional(),
		}).optional(),
	})),

	initialValues: {
		firstName: '',
		lastName: '',
		birthday: '',
		gender: 'male',
	},
})

const submitForm = AccountForm.handleSubmit(async (values) => {
	const { $api } = useNuxtApp()
	isSaving.value = true
	return $api<unknown>('/my/update-Information', {
		method: 'PUT',
		body: values,
	})
		.then(() => {
			authStore.fetchUser()
			toast.success(t('form.profile-updated-success'))
		})
		.finally(() => {
			nextTick(() => isSaving.value = false)
		})
})

watch(() => authStore.user, (user: User | undefined) => {
	if (!user)
		return

	const data = user as User

	if (user?.birthday) {
		data.birthday = new Date(user.birthday).toISOString().split('T')[0] as string
	}

	AccountForm.setValues(data)
}, { immediate: true, deep: true })

const attrs = useAttrs()
</script>

<template>
	<Card
		v-bind="attrs"
		class="w-full h-full !overflow-auto"
	>
		<CardHeader class="max-sm:hidden flex-row">
			<span class="font-bold text-xl">{{ $t('profile.page-title') }}</span>
			<Badge
				v-if="authStore.isAwaiting"
				class="bg-yellow-600 text-2xs text-white drop-shadow-md ms-5"
			>
				{{ $t('form.is-awaiting') }}
			</Badge>
		</CardHeader>

		<CardContent class="flex flex-col flex-grow h-full">
			<div class="grid grid-cols-2 gap-6 max-md:grid-cols-1 max-sm:pt-4">
				<FormField
					v-slot="{ componentField }"
					name="firstName"
				>
					<FormItem>
						<FormLabel class="font-bold">
							{{ $t('form.first-name') }}*
						</FormLabel>
						<FormControl>
							<Input
								type="text"
								:placeholder="$t('form.first-name') "
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<FormField
					v-slot="{ componentField }"
					name="lastName"
				>
					<FormItem>
						<FormLabel class="font-bold">
							{{ $t('form.last-name') }}*
						</FormLabel>
						<FormControl>
							<Input
								type="text"
								:placeholder="$t('form.last-name') "
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<div class="flex flex-col gap-2 text-sm">
					<div class="flex justify-between items-center">
						<label
							for="phone"
							class="font-bold"
						>
							{{ $t('form.phone') }}
						</label>

						<button
							class="text-sm text-primary-600 font-bold flex items-center gap-1"
							@click="editModal = 'phone'"
						>
							<Icon name="lucide:pencil" />
							<span>{{ $t('form.edit') }}</span>
						</button>
					</div>
					<div class="flex items-center gap-2 bg-gray-100 rounded border border-gray-300 h-11 ltr:flex-row-reverse">
						<input
							id="phone"
							:value="AccountForm.values.phone.number"
							type="text"
							:disabled="true"
							class="w-full p-2 outline-none no-spinner rounded h-11 bg-transparent"
							:placeholder="$t('form.phone') "
						>
						<div
							class="flex border-e px-4 border-gray-300"
							dir="ltr"
						>
							+{{ AccountForm.values.phone.code }}
						</div>
					</div>
				</div>

				<div class="flex flex-col gap-2 text-sm">
					<div class="flex justify-between items-center">
						<label
							for="email"
							class="font-bold"
						>
							{{ $t('form.email') }}
						</label>

						<button
							class="text-sm text-primary-600 font-bold flex items-center gap-1"
							@click="editModal = 'email'"
						>
							<Icon name="lucide:pencil" />
							<span>{{ $t('form.edit') }}</span>
						</button>
					</div>
					<input
						id="email"
						:value="AccountForm.values.email"
						type="email"
						:disabled="true"
						name="email"
						class="w-full p-2 border outline-none no-spinner rounded h-11 border-gray-300"
						:placeholder="$t('form.email') "
					>
				</div>

				<div class="flex flex-col gap-2 text-sm">
					<div class="flex justify-between items-center">
						<label
							for="password"
							class="font-bold"
						>
							{{ $t('form.password') }}
						</label>

						<button
							class="text-sm text-primary-600 font-bold flex items-center gap-1"
							@click="editModal = 'password'"
						>
							<Icon name="lucide:pencil" />
							<span>{{ $t('form.edit') }}</span>
						</button>
					</div>
					<input
						id="password"
						value="xxxxxxxxxx"
						type="password"
						:disabled="true"
						name="password"
						class="w-full p-2 border outline-none no-spinner rounded h-11 border-gray-300"
						:placeholder="$t('form.password') "
					>
				</div>

				<FormField
					v-slot="{ componentField }"
					name="birthday"
				>
					<FormItem>
						<FormLabel class="font-bold">
							{{ $t('form.date-of-birth') }}
						</FormLabel>
						<FormControl>
							<Input
								type="date"
								:placeholder="$t('form.date-of-birth') "
								:max="formattedMaxDate as string"
								v-bind="componentField"
								class="text-start"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<div class="flex flex-col gap-2 text-sm">
					<FormField
						v-slot="{ componentField }"
						name="gender"
					>
						<FormItem>
							<FormLabel class="font-bold">
								{{ $t('form.gender') }}
							</FormLabel>
							<FormControl>
								<Select
									id="gender"
									v-bind="componentField"
								>
									<SelectTrigger>
										<SelectValue :placeholder="$t('form.gender')" />
									</SelectTrigger>
									<SelectContent>
										<SelectGroup>
											<SelectItem
												:value="null"
											>
												{{ $t('form.select') }}
											</SelectItem>

											<SelectItem
												value="male"
											>
												{{ $t('form.male') }}
											</SelectItem>

											<SelectItem value="female">
												{{ $t('form.female') }}
											</SelectItem>
										</SelectGroup>
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>
				</div>
			</div>
		</CardContent>

		<CardFooter class="flex justify-end items-end max-sm:justify-center">
			<Button
				class="min-w-32 max-sm:w-full"
				:loading="isSaving"
				@click.prevent="() => submitForm()"
			>
				{{ $t('form.save') }}
			</Button>
		</CardFooter>
	</Card>
	<ProfilePagesProfilePassword
		v-if="editModal === 'password'"
		@close:modal="() => { editModal = null }"
	/>

	<ProfilePagesProfileEmail
		v-if="editModal === 'email'"
		@close:modal="() => { editModal = null }"
	/>

	<ProfilePagesProfilePhone
		v-if="editModal === 'phone'"
		@close:modal="() => { editModal = null }"
	/>
</template>
