<script setup lang="ts">
import OrderInformation from '~/components/profile/pages/orders/information.vue'
import CancelOrderModal from '~/components/profile/pages/orders/cancel-order.vue'
import type { Order } from '~/interfaces/auth/order.js'

const route = useRoute()
const orderId = computed(() => route.params?.orderId)
const { data, error, status } = await useApi<Order>(`/my/orders/${orderId.value}`)

if (error.value) {
	console.log('Error on fetching order ', error.value)
}

const order = computed(() => data.value as Order)
const loading = computed(() => status.value !== 'success')

const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const isCancelOrder = ref(false)
const isDraftOrder = computed(() => order.value?.status === 'draft')
definePageMeta({
	name: 'order-tracking',
	parent: 'orders',
})
</script>

<template>
	<div class="flex w-full min-h-screen overflow-auto bg-body">
		<div class="flex w-full flex-col gap-4 h-full overflow-y-auto">
			<Card>
				<CardHeader class="gap-6">
					<div class="flex w-full gap-4 justify-between items-center">
						<template v-if="loading">
							<Skeleton class="h-6 w-1/2" />
						</template>
						<template v-else>
							<div class="flex items-center gap-4">
								<NuxtLinkLocale
									class="flex flex-row gap-2 items-center text-lg font-bold"
									to="/my/orders"
								>
									<Icon
										name="lucide:chevron-right"
										class="text-2xl"
										:class="{ 'rotate-180': !isRtl }"
									/>
									<span>
										{{ $t('orders.your-order-number', { number: order.orderId }) }}
									</span>
								</NuxtLinkLocale>

								<StatusOrder :status="order.status" />
							</div>

							<Button
								v-if="!isDraftOrder"
								variant="danger"
								:size="'sm'"
								@click="isCancelOrder=true"
							>
								{{ $t("orders.cancel-order") }}
							</Button>
						</template>
					</div>

					<div
						v-if="!isDraftOrder"
						class="relative flex w-full bg-sky-50 p-4 rounded-md gap-2 items-center justify-between font-bold z-0"
					>
						<template v-if="loading">
							<Skeleton class="h-10 w-1/3" />
							<Skeleton class="h-10 w-1/3" />
							<Skeleton class="h-10 w-1/3" />
						</template>
						<template v-else>
							<div
								class="flex flex-col px-8 bg-sky-50 items-center justify-center gap-2"
								:class="{ active: order.status ==='received' }"
							>
								<div class="icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200">
									<Icon
										name="ui:order-in-progress"
										size="30px"
									/>
								</div>
								<span>{{ $t('orders.order-executing') }}</span>
							</div>

							<div
								class="flex flex-col px-8 bg-sky-50 items-center justify-center gap-2"
								:class="{ active: order.status === 'active' }"
							>
								<div class="icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200">
									<Icon
										name="ui:order-loading"
										size="30px"
									/>
								</div>
								<span>{{ $t('orders.order-on-way') }}</span>
							</div>

							<div
								class="flex flex-col px-8 bg-sky-50 items-center justify-center gap-2"
								:class="{ active: order.status === 'completed' }"
							>
								<div class="icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200">
									<Icon
										name="ui:order-loading"
										size="30px"
									/>
								</div>
								<span>{{ $t('orders.order-delivered') }}</span>
							</div>

							<div class="flex max-w-3xl h-[2px] bg-gray-300 absolute top-auto bottom-auto inset-0 left-0 -z-10 mx-auto" />
						</template>
					</div>
				</CardHeader>
			</Card>

			<OrderInformation
				:loading="loading"
				:order="order"
			/>
		</div>

		<CancelOrderModal
			v-if="isCancelOrder"
			@close:modal="isCancelOrder=false"
		/>
	</div>
</template>

<style scoped lang="scss">
.active {
  @apply text-yellow-400;

  .icon-box {
    @apply bg-yellow-400 !important;
  }

  .iconify {
    @apply text-white
  }
}
</style>
