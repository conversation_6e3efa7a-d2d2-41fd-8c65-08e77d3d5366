import { useRoute } from 'vue-router'

/**
 * Function to checking the hash page scrolling
 * @param offset = 150 by default
 */
export function useScrollTo(offset = 150) {
	const route = useRoute()

	const scrollToElement = (elementId: string) => {
		const element = document.getElementById(elementId)

		if (!element) return

		window.scrollTo({
			top: element?.offsetTop - offset,
			behavior: 'smooth',
		})
	}

	watch(() => route, (route) => {
		if (!route?.hash) return

		return setTimeout(() => {
			scrollToElement((route.hash as string).replace('#', ''))
		}, 100)
	}, { immediate: true, deep: true })

	return { scrollToElement }
}
