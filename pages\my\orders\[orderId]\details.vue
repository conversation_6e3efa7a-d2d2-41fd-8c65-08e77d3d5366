<script setup lang="ts">
import OrderInformation from '~/components/profile/pages/orders/information.vue'
import ProductReview from '~/components/profile/pages/orders/product-review.vue'
import DeliveryReview from '~/components/profile/pages/orders/delivery-review.vue'
import type { Order } from '~/interfaces/auth/order.js'

const route = useRoute()
const orderId = computed(() => route.params?.orderId)
const { data, error, status } = await useApi<Order>(`/orders/${orderId.value}`)

if (error.value) {
	console.log('Error on fetching order ', error.value)
}

const order = computed(() => data.value as Order)
const loading = computed(() => status.value !== 'success')
definePageMeta({
	name: 'order-details',
	parent: 'orders',
})
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const isProductReview = ref(false)
const isDeliveryReview = ref(false)
const hasDeliveryNote = computed(() => ['received', 'completed'].includes(order.value.status))
</script>

<template>
	<div
		class="flex w-full min-h-screen overflow-auto bg-body"
	>
		<div class="flex w-full flex-col gap-4 h-full overflow-y-auto">
			<Card>
				<CardHeader class="gap-6">
					<template v-if="loading">
						<Skeleton class="h-6 w-1/2" />
					</template>
					<template v-else>
						<div class="flex w-full gap-4">
							<NuxtLinkLocale
								class="flex flex-row gap-2 items-center text-lg font-bold"
								to="/my/orders?tab=history"
							>
								<Icon
									name="lucide:chevron-right"
									class="text-2xl"
									:class="{ 'rotate-180': !isRtl }"
								/>
								<span>
									{{ $t('orders.your-order-number', { number: order.orderId }) }}
								</span>
							</NuxtLinkLocale>

							<StatusOrder :status="order.status" />
						</div>
						<div
							v-if="hasDeliveryNote"
							class="flex"
						>
							<div class="flex w-full bg-sky-50 p-4 rounded-md gap-2 items-center">
								<div class="flex p-2 rounded-full bg-green-1000 justify-center items-center">
									<Icon
										name="ui:received"
										class="text-white"
										size="16px"
									/>
								</div>
								<span class="text-base font-semibold">
									{{ $t('orders.received-at', { date: order?.createdAt }) }}
								</span>
							</div>
						</div>
					</template>
				</CardHeader>
			</Card>

			<Card v-if="order?.status !== 'draft'">
				<CardHeader>
					<template v-if="loading">
						<Skeleton class="h-6 w-1/2" />
					</template>
					<template v-else>
						<span class="text-xl font-bold">{{ $t('orders.review-experience-text') }}</span>
					</template>
				</CardHeader>

				<CardContent>
					<template v-if="loading">
						<div class="flex w-full gap-3">
							<Skeleton class="h-24 w-1/2" />
							<Skeleton class="h-24 w-1/2" />
						</div>
					</template>
					<template v-else>
						<div class="flex gap-4 items-center">
							<div class="flex gap-4 p-6 rounded-lg border flex-col w-1/2 justify-center items-center">
								<Icon
									name="ui:order-review"
									size="50px"
									class="text-primary-600"
								/>
								<span class="text-base font-bold max-sm:text-sm text-center">
									{{ $t('orders.help-people-buy') }}
								</span>

								<Button
									class="w-full mt-2"
									@click="isProductReview = true"
								>
									{{ $t('orders.review-experience-btn') }}
								</Button>
							</div>

							<div class="flex gap-4 p-6 rounded-lg border flex-col w-1/2 justify-center items-center">
								<Icon
									name="ui:delivery-truck"
									size="50px"
									class="text-primary-600"
								/>
								<span class="text-base font-bold max-sm:text-sm text-center">
									{{ $t('orders.review-delivered-experience-text') }}
								</span>

								<Button
									class="w-full mt-2"
									@click="isDeliveryReview = true"
								>
									{{ $t('orders.review-delivered-experience-btn') }}
								</Button>
							</div>
						</div>
					</template>
				</CardContent>
			</Card>

			<OrderInformation
				:loading="loading"
				:order="order"
			/>
		</div>

		<ProductReview
			v-if="isProductReview"
			@close:modal="isProductReview=false"
		/>

		<DeliveryReview
			v-if="isDeliveryReview"
			:shipping-carrier-id="order.shippingCarrierId"
			@close:modal="isDeliveryReview=false"
		/>
	</div>
</template>
