{"root": true, "env": {"browser": true, "es2021": true, "node": true, "vue/setup-compiler-macros": true}, "extends": ["@nuxtjs/eslint-config-typescript", "plugin:vuetify/base"], "parserOptions": {"ecmaVersion": "latest", "parser": "@typescript-eslint/parser", "sourceType": "module"}, "rules": {"indent": ["error", "tab"], "comma-dangle": ["error", "always-multiline"], "space-before-function-paren": ["error", {"anonymous": "always", "named": "never", "asyncArrow": "always"}], "vue/html-indent": ["error", "tab", {"attribute": 1, "baseIndent": 1, "closeBracket": 0, "alignAttributesVertically": true, "ignores": []}], "vue/script-indent": ["error", "tab", {"baseIndent": 0, "switchCase": 0, "ignores": []}], "vue/no-v-text-v-html-on-component": "off", "vue/no-use-v-if-with-v-for": "off", "vue/valid-v-slot": "off", "vue/no-multiple-template-root": "off", "no-tabs": "off", "vue/multi-word-component-names": "off", "no-console": "off", "vue/no-v-html": "off"}}