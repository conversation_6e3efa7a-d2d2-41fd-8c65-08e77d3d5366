export interface WishlistResponse {
	items?: Wishlist[]
	pagination?: Pagination
}

export interface Pagination {
	total?: number
	count?: number
	perPage?: number
	page?: number
	lastPage?: number
}

export interface Wishlist {
	wishlistId?: number
	productId?: number
	name?: string
	brandId?: unknown
	productGroupId?: unknown
	description?: string
	type?: string
	slug?: string
	isListed?: boolean
	SKU?: unknown
	variationAttributes?: string
	avgRate?: number
	brand?: Brand
	categories?: Categories[]
	variance?: Variance
	media?: Media
}

export interface Brand {
	brandId?: number
	name?: string
	slug?: string
	metaTitle?: string
	metaDescription?: string
}

export interface Variance {
	varianceId?: number
	auctionId?: unknown
	name?: string
	slug?: string
	brandId?: number
	SKU?: unknown
	type?: string
	stock?: unknown
}

export interface Media {
	gallery?: Gallery[]
	cover?: Cover[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}

export interface Cover {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}

export interface Categories {
	categoryId?: number
	name?: string
	slug?: string
	parentId?: unknown
	publishedAt?: unknown
	metaTitle?: string
	metaDescription?: string
	isShowBrandIngListing?: boolean
}
