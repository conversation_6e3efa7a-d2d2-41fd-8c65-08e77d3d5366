<script setup lang="ts">
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Wishlist } from '~/interfaces/wishlist/list'
import { useAuthStore } from '~/store/useAuthStore.client'
import { useFavoriteStore } from '~/store/useFavoriteStore'

const { isOpen = true } = defineProps<{
	isOpen?: boolean
}>()

const emit = defineEmits<{
	(event: 'set:wish-list', value: boolean): void
}>()

const favoriteStore = useFavoriteStore()
const authStore = useAuthStore()
const list = computed(() => (favoriteStore?.list || []) as Wishlist[])
const listLength = computed(() => list.value?.length)
const isEmptyList = computed(() => !listLength.value)
const isLoggedIn = computed(() => authStore.isLoggedIn)
const pagination = computed(() => favoriteStore.pagination)
const hasPagination = computed(() => favoriteStore.hasPagination)

const { locale, t: $t } = useI18n()
const direction = computed(() => locale.value !== 'ar' ? 'right' : 'left')
const page = ref(1)

const openWishListPage = () => {
	navigateTo('/my/wishlist')
}
onMounted(() => {
	favoriteStore.fetchMounted()
})

const onLoadMore = () => {
	favoriteStore.fetchMounted(1 + page.value)
}

const handleOnClose = (value: boolean) => {
	if (value) return

	emit('set:wish-list', false)
}
</script>

<template>
	<Drawer
		id="wish-list"
		:open="isOpen"
		:direction="direction"
		:dismissible="true"
		aria-describedby="Wish list page"
		@update:open="handleOnClose"
	>
		<DrawerContent
			aria-describedby="Wish list page"
			:class="`drawer-${locale}`"
			class="max-w-lg w-full h-dvh max-sm:rounded-none"
		>
			<DrawerHeader class="border-b border-gray-200 flex justify-between">
				<DrawerTitle class="text-start text-xl font-bold">
					{{
						$t('wish-list.title', {
							item: $t('wish-list.item', { count: listLength }), number: listLength,
						})
					}}
				</DrawerTitle>
				<DrawerDescription class="hidden">
					drawer description
				</DrawerDescription>

				<button
					class="w-8 h-8 rounded-full justify-center flex items-center border border-gray-400 mx-2"
					@click="() => emit('set:wish-list', false)"
				>
					<Icon
						name="lucide:x"
						class="cursor-pointer w-5 h-5 text-gray-700"
					/>
				</button>
			</DrawerHeader>
			<DrawerWishListItems
				@add:wish-list="() => emit('set:wish-list', false)"
			/>

			<DrawerFooter
				v-if="!isEmptyList"
				class="flex flex-row items-center gap-2 justify-between"
			>
				<template v-if="isLoggedIn">
					<Button
						variant="default"
						class="w-full"
						@click="openWishListPage"
					>
						{{ $t('wish-list.show-list-btn') }}
					</Button>
				</template>
				<template v-else-if="hasPagination">
					<Paginate
						:items-per-page="pagination?.perPage"
						:total="pagination?.lastPage"
						:sibling-count="1"
						:show-edges="true"
						:default-page="pagination.page"
						@update:page="onLoadMore"
					/>
				</template>
			</DrawerFooter>
		</DrawerContent>
	</Drawer>
</template>

<style scoped lang="scss">
:global(.drawer-en) {
  left: auto !important;
  right: 0 !important;
}

:global(.drawer-ar) {
  left: 0 !important;
  right: auto !important;
}
</style>
