export default defineEventHandler(async (event) => {
	try {
		// Get the client's real IP address
		let clientIP = getRequestIP(event) || getHeader(event, 'x-forwarded-for') || getHeader(event, 'x-real-ip')

		// check if ip has comma
		if (clientIP && clientIP.includes(',')) {
			// If there are multiple IPs, take the first one
			const ips = clientIP.split(',')
			if (ips.length > 0) {
				clientIP = ips[0].trim()
			}
		}

		// Make the request to ipwho.is with the client's IP
		const response = await $fetch('https://ipwho.is', {
			method: 'GET',
			query: clientIP ? { ip: clientIP } : undefined,
			headers: {
				'User-Agent': 'ActionMobile/2.0',
				'Accept': 'application/json',
			},
		})

		if (response && response.message === 'Reserved range') {
			return {
				...response,
				country_code: 'JO', // Default to Jordan
				country: 'Jordan',
				region: 'Amman',
				city: 'Amman',
				timezone: {
					id: 'Asia/Amman',
				},
			}
		}

		// Return the response from ipwho.is
		return response
	} catch {
		// Return a fallback response in case of error
		return {
			success: false,
			country_code: 'JO', // Default to Jordan
			country: 'Jordan',
			region: 'Amman',
			city: 'Amman',
			timezone: {
				id: 'Asia/Amman',
			},
			error: 'Unable to determine location',
		}
	}
})
