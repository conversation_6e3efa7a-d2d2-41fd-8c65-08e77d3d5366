import fs from 'node:fs'
import path from 'node:path'

interface TranslationData {
	[key: string]: string | TranslationData
}

class TestI18n {
	private translations: Record<string, TranslationData> = {}
	private currentLocale = 'ar'

	constructor() {
		this.loadTranslations()
	}

	private loadTranslations() {
		try {
			// Load English translations
			const enPath = path.join(process.cwd(), 'i18n/locales/en.json')
			if (fs.existsSync(enPath)) {
				try {
					const enContent = fs.readFileSync(enPath, 'utf-8')
					this.translations.en = JSON.parse(enContent)
				} catch (error) {
					console.warn('❌ Error loading English translations:', error.message)
				}
			}

			// Load Arabic translations
			const arPath = path.join(process.cwd(), 'i18n/locales/ar.json')
			if (fs.existsSync(arPath)) {
				try {
					const arContent = fs.readFileSync(arPath, 'utf-8')
					this.translations.ar = JSON.parse(arContent)
				} catch (error) {
					console.warn('❌ Error loading Arabic translations:', error.message)
					console.warn('File path:', arPath)
				}
			}
		} catch (error) {
			console.warn('Could not load translation files:', error)
		}
	}

	setLocale(locale: string) {
		this.currentLocale = locale
	}

	t(key: string, locale?: string): string {
		const targetLocale = locale || this.currentLocale
		const translation = this.getNestedValue(this.translations[targetLocale], key)

		if (translation && typeof translation === 'string') {
			return translation
		}

		// Fallback to English if not found
		if (targetLocale !== 'en') {
			const fallback = this.getNestedValue(this.translations.en, key)
			if (fallback && typeof fallback === 'string') {
				return fallback
			}
		}

		// Return key if translation not found
		return key
	}

	private getNestedValue(obj: TranslationData, key: string): string | TranslationData | undefined {
		return key.split('.').reduce((current: TranslationData | string | undefined, keyPart: string) => {
			if (current && typeof current === 'object') {
				return current[keyPart]
			}
			return undefined
		}, obj)
	}
}

export const testI18n = new TestI18n()
