import { test, expect } from '@playwright/test'
import { AuthHelpers, AppHelpers, TestData } from './helpers/test-helpers'
import { testI18n } from './helpers/i18n-helper'

test.describe('Authentication', () => {
	let authHelpers: AuthHelpers
	let appHelpers: AppHelpers

	test.beforeEach(async ({ page }) => {
		authHelpers = new AuthHelpers(page)
		appHelpers = new AppHelpers(page)
		await page.goto('/')
		await appHelpers.waitForPageLoad()
	})

	// test.describe('Login Modal', () => {
	// 	test('should open login modal from URL parameter', async ({ page }) => {
	// 		await page.goto('/?auth=login')
	// 		await authHelpers.expectLoginModalVisible()
	// 	})

	// 	test('should display all required form elements', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()

	// 		// Check if all form elements are visible
	// 		await expect(authHelpers.phoneInput).toBeVisible()
	// 		await expect(authHelpers.passwordInput).toBeVisible()
	// 		await expect(authHelpers.loginButton).toBeVisible()
	// 		await expect(authHelpers.signUpLink).toBeVisible()
	// 		await expect(authHelpers.forgotPasswordLink).toBeVisible()
	// 	})

	// 	// test('should close login modal when close button is clicked', async ({ page: _page }) => {
	// 	// 	await authHelpers.openLoginModal()
	// 	// 	await authHelpers.closeButton.click()
	// 	// 	await authHelpers.expectLoginModalHidden()
	// 	// })

	// 	test('should navigate to signup when signup link is clicked', async ({ page }) => {
	// 		await authHelpers.openLoginModal()
	// 		await authHelpers.signUpLink.click()

	// 		// Should navigate to signup page
	// 		await expect(page).toHaveURL(/auth=signup/)
	// 	})

	// 	test('should navigate to forgot password when forgot password link is clicked', async ({ page }) => {
	// 		await authHelpers.openLoginModal()
	// 		await authHelpers.forgotPasswordLink.click()

	// 		// Should navigate to forgot password page
	// 		await expect(page).toHaveURL(/auth=forgot-password/)
	// 	})
	// })

	test.describe('Login Form Validation', () => {
		test('should show validation error for invalid phone number', async ({ page: _page }) => {
			await authHelpers.openLoginModal()
			await authHelpers.fillLoginForm(TestData.invalidPhone.phone, TestData.invalidPhone.password).then(() => {
				authHelpers.phoneInput.focus()
			})
			await authHelpers.submitLogin()

			// Should show phone validation error
			// await authHelpers.expectErrorMessage(testI18n.t('error.invalid-username-password'))
			await expect(authHelpers.loginModal.locator('.base-phone-input ~ .text-destructive')).toContainText(testI18n.t('error.phone-number-invalid'))
		})

		// 	test('should show validation error for empty password', async ({ page: _page }) => {
		// 		await authHelpers.openLoginModal()
		// 		await authHelpers.fillLoginForm(TestData.validCredentials.phone, '')
		// 		await authHelpers.submitLogin()

		// 		// Should show password validation error
		// 		await authHelpers.expectErrorMessage()
		// 	})

		// 	test('should disable login button when form is invalid', async ({ page: _page }) => {
		// 		await authHelpers.openLoginModal()

		// 		// Initially button should be disabled (empty form)
		// 		await expect(authHelpers.loginButton).toBeDisabled()

		// 		// Fill only phone, button should still be disabled
		// 		await authHelpers.phoneInput.fill(TestData.validCredentials.phone)
		// 		await expect(authHelpers.loginButton).toBeDisabled()

	// 		// Fill password too, button should be enabled
	// 		await authHelpers.passwordInput.fill(TestData.validCredentials.password)
	// 		await expect(authHelpers.loginButton).toBeEnabled()
	// 	})
	})

	// test.describe('Login Functionality', () => {
	// 	test('should show error message for invalid credentials', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()
	// 		await authHelpers.fillLoginForm(TestData.invalidCredentials.phone, TestData.invalidCredentials.password)
	// 		await authHelpers.submitLogin()

	// 		// Should show invalid credentials error
	// 		await authHelpers.expectErrorMessage()

	// 		// Modal should still be visible
	// 		await authHelpers.expectLoginModalVisible()
	// 	})

	// 	test('should show loading state during login process', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()
	// 		await authHelpers.fillLoginForm(TestData.validCredentials.phone, TestData.validCredentials.password)

	// 		// Click login and immediately check for loading state
	// 		await authHelpers.loginButton.click()

	// 		// Button should show loading state
	// 		await expect(authHelpers.loginButton).toBeDisabled()
	// 		await expect(authHelpers.loginButton).toContainText(/loading|submitting/i).catch(() => {
	// 			// If no loading text, at least button should be disabled
	// 			expect(authHelpers.loginButton).toBeDisabled()
	// 		})
	// 	})

	// 	test.skip('should successfully login with valid credentials', async ({ page }) => {
	// 		// Note: This test is skipped because it requires valid test credentials
	// 		// To enable this test, you need to:
	// 		// 1. Set up test user credentials in your test environment
	// 		// 2. Update TestData.validCredentials with real test credentials
	// 		// 3. Remove the .skip() from this test

	// 		await authHelpers.login(TestData.validCredentials.phone, TestData.validCredentials.password)

	// 		// Should close login modal
	// 		await authHelpers.expectLoginModalHidden()

	// 		// Should redirect to home page without auth parameter
	// 		await expect(page).toHaveURL(/^\/$/)

	// 		// Should show user menu in header
	// 		await expect(appHelpers.userMenuButton).toBeVisible()
	// 	})
	// })

	// test.describe('Phone Input Interaction', () => {
	// 	test('should format phone number correctly', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()

	// 		// Type a phone number
	// 		await authHelpers.phoneInput.fill('791234567')

	// 		// Check if it gets formatted (this depends on your phone input component)
	// 		const phoneValue = await authHelpers.phoneInput.inputValue()
	// 		expect(phoneValue).toBeTruthy()
	// 	})

	// 	test('should handle country code selection', async ({ page }) => {
	// 		await authHelpers.openLoginModal()

	// 		// Look for country selector (adjust selector based on your implementation)
	// 		const countrySelector = authHelpers.loginModal.locator('[data-testid="country-selector"]')
	// 		if (await countrySelector.isVisible()) {
	// 			await countrySelector.click()
	// 			// Select a different country (adjust based on your implementation)
	// 			await page.locator('text=United States').click()
	// 		}
	// 	})
	// })

	// test.describe('Password Field Interaction', () => {
	// 	test('should toggle password visibility', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()
	// 		await authHelpers.passwordInput.fill('testpassword')

	// 		// Find password toggle button
	// 		const passwordToggle = authHelpers.loginModal.locator('button[aria-label*="password"], button:has(svg[name*="eye"])')

	// 		if (await passwordToggle.isVisible()) {
	// 			// Initially password should be hidden
	// 			await expect(authHelpers.passwordInput).toHaveAttribute('type', 'password')

	// 			// Click toggle to show password
	// 			await passwordToggle.click()
	// 			await expect(authHelpers.passwordInput).toHaveAttribute('type', 'text')

	// 			// Click toggle to hide password
	// 			await passwordToggle.click()
	// 			await expect(authHelpers.passwordInput).toHaveAttribute('type', 'password')
	// 		}
	// 	})
	// })

	// test.describe('Accessibility', () => {
	// 	test('should be accessible with keyboard navigation', async ({ page }) => {
	// 		await authHelpers.openLoginModal()

	// 		// Tab through form elements
	// 		await page.keyboard.press('Tab')
	// 		await expect(authHelpers.phoneInput).toBeFocused()

	// 		await page.keyboard.press('Tab')
	// 		await expect(authHelpers.passwordInput).toBeFocused()

	// 		await page.keyboard.press('Tab')
	// 		await expect(authHelpers.loginButton).toBeFocused()
	// 	})

	// 	test('should have proper ARIA attributes', async ({ page: _page }) => {
	// 		await authHelpers.openLoginModal()

	// 		// Check modal has proper role
	// 		await expect(authHelpers.loginModal).toHaveAttribute('role', 'dialog')

	// 		// Check form inputs have proper labels
	// 		await expect(authHelpers.phoneInput).toHaveAttribute('aria-label')
	// 		await expect(authHelpers.passwordInput).toHaveAttribute('aria-label')
	// 	})
	// })
})
