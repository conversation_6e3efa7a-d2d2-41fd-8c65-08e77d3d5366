<script setup lang="ts">
import { Card, CardContent } from '~/components/ui/card'
</script>

<template>
	<Card class="h-full py-6 bg-sky-50">
		<CardContent class="flex flex-col gap-4 justify-center">
			<div class="flex gap-2 items-center justify-center xs:flex-row sm:flex-col-reverse lg:flex-row">
				<div class="flex flex-col gap-2 flex-nowrap max-sm:gap-6">
					<div class="flex items-center">
						<Icon
							name="lucide:truck"
							width="30px"
							class="text-primary-600"
						/>
						<span class="text-primary-600 text-lg font-semibold text-nowrap ms-1 md:font-md">{{ $t("home.widget-delivery") }}</span>
					</div>
					<span class="text-gray-600 text-2xl font-bold md:font-lg">{{ $t("home.widget-delivery-time") }}</span>
				</div>
				<NuxtImg
					src="/images/fast-delivery.png"
					class="w-full h-auto lg:max-w-28 md:max-w-40 max-sm:w-36 max-sm:h-20"
					:alt="$t('home.widget-delivery-time')"
					:title="$t('home.widget-delivery-time')"
					width="100"
					height="100"
					loading="lazy"
				/>
			</div>

			<div class="flex justify-center items-center mt-5">
				<Button
					variant="default"
					size="lg"
				>
					<span>{{ $t('home.widget-buy-now') }}</span>
				</Button>
			</div>
		</CardContent>
	</Card>
</template>
