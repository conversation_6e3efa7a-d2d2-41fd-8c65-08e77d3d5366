export interface AuthPhone {
	number?: string
	iso?: string
	code?: string
}

export interface AuthForm {
	first_name?: string
	last_name?: string
	phone?: AuthPhone
	password?: string
	confirm_password?: string
	new_password?: string
	email?: string
}

export interface AuthRegisterPayload {
	firstName?: string
	lastName?: string
	phone?: AuthPhone
	password?: string
}

export interface AuthLoginPayload {
	phone?: AuthPhone
	password?: string
}

export interface AuthPassPayload {
	oldPassword: string
	password: string
	passwordConfirmation: string
}

export interface AuthResetPassPayload {
	password: string
	passwordConfirmation: string
	userId: number | null
}

export interface AuthPassResponse {
	passwordChanged: boolean
}
