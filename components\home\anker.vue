<script setup lang="ts">
import type { Brand, BrandList } from '~/interfaces/brands/brand'
import type { Details } from '~/interfaces/product/details'

interface ProductResponse {
	items?: Details[]
}

const { data: brandData } = await useApi<BrandList>('brands', {
	query: {
		perPage: 1,
	},
})

const brands = computed(() => (brandData.value as BrandList)?.items as Brand[])
const firstBrand = computed<Brand>(() => brands.value?.[0] as Brand)

/** fetch products by brands id **/
const { data, error, status } = await useApi<ProductResponse>('products', {
	query: { brandId: firstBrand.value?.brandId, perPage: 2 },
	watch: [firstBrand],
})

const response = computed(() => data.value as ProductResponse)
const products = computed(() => (response.value?.items as Details[]))

if (error.value) {
	console.error('Error fetching new arrival:', error.value)
}

const loading = computed(() => status.value !== 'success')
</script>

<template>
	<div class="list col-span-3 flex w-full h-full shadow bg-white rounded-lg max-sm:!p-2">
		<template v-if="loading || !firstBrand">
			<div class="image">
				<Skeleton class="w-full h-44" />
			</div>
		</template>
		<template v-else>
			<div class="image">
				<NuxtImg
					:src="firstBrand?.media?.logoName?.src"
					provider="backend"
					class="w-full h-full object-contain"
					:alt="firstBrand?.name ?? $t('app.marketing-alt')"
					:title="firstBrand?.name ?? $t('app.marketing-alt')"
					format="webp"
					width="300"
					height="93"
				/>
			</div>
		</template>
		<template v-if="loading || !firstBrand">
			<div class="flex gap-4 product">
				<Skeleton class="w-full h-44" />
				<Skeleton class="w-full h-44" />
			</div>
		</template>
		<template v-else>
			<div class="flex gap-4 product">
				<ProductCard
					v-for="product in products"
					:key="`banner-product-${product?.productId}`"
					:product="product"
					variant="horizontal"
				/>
			</div>
		</template>
	</div>
</template>

<style scoped>
.list {
  @apply grid gap-2 items-center justify-evenly py-6 px-4 md:grid-cols-2 xs:grid-cols-1 lg:grid-cols-3;

  .image {
    @apply flex items-center justify-center md:col-span-2 xs:col-span-1 lg:col-span-1;

    img {
      @apply h-24 w-80
    }
  }

  .product {
    @apply col-span-2 max-sm:flex-col gap-4 p-2 flex items-center justify-between;

    :deep(.card-horizontal) {
      @apply md:w-1/2 xs:max-w-full
    }
  }
}
</style>
