{
	"workbench.colorCustomizations": {
		"commandCenter.border": "#e7e7e799",
		"sash.hoverBorder": "#893477",
		"titleBar.activeBackground": "#893477",
		"titleBar.activeForeground": "#e7e7e7",
		"titleBar.inactiveBackground": "#893477",
		"titleBar.inactiveForeground": "#e7e7e799"
	},
	"nuxtr.defaultPackageManager": "NPM",
	"cSpell.words": [
		"arcticons",
		"btns",
		"cacheable",
		"defu",
		"formkit",
		"iconify",
		"jsonld",
		"klona",
		"lastmod",
		"metatrader",
		"Nuxt",
		"nuxtjs",
		"pinia",
		"shadcn",
		"sidebase",
		"unocss",
		"Vite",
		"vuetify",
		"vueuse",
		"zadigetvoltaire"
	],
	"eslint.format.enable": true,
	"editor.insertSpaces": false,
	"editor.detectIndentation": false,
	"editor.tabSize": 3,
	"editor.linkedEditing": false,
	"javascript.preferences.renameMatchingJsxTags" : true,
	"typescript.preferences.renameMatchingJsxTags" : true,
	"editor.codeActionsOnSave": {
		// "source.fixAll.eslint": "never"
	},
	"editor.formatOnSave": true,
	"editor.formatOnType": false,
	"editor.wordBasedSuggestions": "off",
	"editor.tokenColorCustomizations": {
		"textMateRules": [
			{
				"scope": "invalid.illegal.character-not-allowed-here.html",
				"settings": {
					"foreground": "#808080"
				}
			}
		]
	},
	"html.autoClosingTags": true,
	"typescript.tsdk": "node_modules\\typescript\\lib",
	"vue.autoInsert.dotValue": true,
	"vue.inlayHints.inlineHandlerLeading": true,
	"vue.inlayHints.missingProps": true,
	"vue.inlayHints.optionsWrapper": true,
	"vue.splitEditors.layout.left": [
		"script",
		"scriptSetup",
		"styles"
	],
	"vue.splitEditors.layout.right": [
		"template",
		"customBlocks"
	],
	"vue.updateImportsOnFileMove.enabled": true,
	"i18n-ally.localesPaths": [
		"i18n/locales"
	],
	"i18n-ally.dirStructure": "file",
	"i18n-ally.displayLanguage": "en",
	"i18n-ally.keystyle": "nested",
	"[jsonc]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"[vue]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"[typescript]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"files.autoSaveDelay": 3000,
	"eslint.codeActionsOnSave.mode": "problems",
	"eslint.enable": true,
	"eslint.useESLintClass": true,
	"vue.codeActions.enabled": true,
	"vue.splitEditors.icon": true,
	"vue.codeLens.enabled": true,
	"vue.server.hybridMode": "auto",
	"vue.format.wrapAttributes": "auto",
	"vue.inlayHints.vBindShorthand": true,
	"iconify.includes": [
		"mdi-light",
		"fe",
		"material-symbols",
		"material-symbols-light",
		"mdi",
		"arcticons"
	],
	"css.customData": [".vscode/tailwind.json"]
}