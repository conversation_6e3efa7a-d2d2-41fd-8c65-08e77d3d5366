<script setup lang="ts">
import { toast } from 'vue-sonner'
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'
import type { AuthPhone } from '~/interfaces/auth/form'
import type FormPhoneValue from '~/interfaces/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { t } = useI18n()
const isOtp = ref<boolean>(false)
const otp = ref<number[]>([])
const isSaving = ref<boolean>(false)

/** Set form for phone object validation and init values **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			phone: z.object({
				number: z.string().nullish(),
				iso: z.string().nullish(),
				code: z.string().nullish(),
				isValid: z.boolean(),
			}).refine(
				phone => phone?.isValid,
				{
					message: t('error.phone-number-invalid'),
					path: ['number'],
				},
			),
		}),
	),
	initialValues: {
		phone: {
			number: '',
			iso: 'JO',
			code: '962',
			isValid: false,
		},
	},
})

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

/** update phone number **/
const submitPhone = Form.handleSubmit(async () => {
	isSaving.value = true
	return authStore.changePhone(Form.values.phone as AuthPhone)
		.then(() => {
			authStore.fetchUser()
			toast.success(t('form.phone-changed-success'))
			return nextTick(() => isOtp.value = true)
		})
		.catch((error) => {
			throw error
		})
		.finally(() => {
			otp.value = []
			nextTick(() => isSaving.value = false)
		})
})

/** submit phone OTP **/
const submitPhoneOTP = async () => {
	isSaving.value = true
	return authStore.verifyPhoneOTP(otpString.value)
		.then(() => {
			toast.success(t('form.otp-validate-success'))
			return nextTick(() => emit('close:modal'))
		})
		.catch((error) => {
			console.log('Error ', error)
		})
		.finally(() => {
			nextTick(() => isSaving.value = false)
		})
}

/** phone number formatted **/
const phoneNumber = computed(() => [
	'+',
	Form.values.phone?.code,
	Form.values.phone?.number,
].join(' '))

/** OTP value string **/
const otpString = computed(() => otp.value.join(''))

/** Is valid OTP **/
const isValidOTP = computed(() => otpString.value.length >= 6)

/** Submit otp when complete and valid **/
const handleCompleteOTP = () => {
	if (isValidOTP.value) {
		submitPhoneOTP()
	}
}
</script>

<template>
	<Modal
		:title="!isOtp?$t('form.change-phone-title'):''"
		:dismissible="true"
		@close="emit('close:modal')"
	>
		<template #body>
			<!-- Phone Section -->
			<div
				v-if="!isOtp"
				class="flex flex-col gap-4 p-4"
			>
				<FormPhone
					:title="$t('form.new-phone-title')"
					:error="Form.errors?.value?.phone"
					@update="(value: FormPhoneValue) => {
						Form.setFieldValue('phone', {
							number: value.nationalNumber,
							code: value.countryCallingCode,
							iso: value.countryCode,
							isValid: value.isValid,
						})
					}"
				/>
			</div>

			<!-- OTP Section -->
			<div
				v-else
				class="flex flex-col gap-4 px-4"
			>
				<div class="relative flex flex-col w-full gap-2 px-4 justify-center items-center">
					<Icon
						name="ui:phone-otp"
						size="120px"
					/>
					<div
						class="flex flex-col w-full py-4 gap-4 justify-center items-center"
					>
						<label
							for="phone"
							class="text-xl font-bold"
						>
							{{ $t('form.verify-phone-title') }}
						</label>
						<PinInput
							id="pin-input"
							v-model="otp"
							type="number"
							@complete="handleCompleteOTP"
						>
							<PinInputGroup>
								<PinInputInput
									v-for="(_, id) in 6"
									:key="`otp-${_}-${id}`"
									:index="id"
								/>
							</PinInputGroup>
						</PinInput>

						<div class="flex flex-col gap-1">
							<div class="flex items-center w-full justify-center text-gray-500 text-sm gap-2">
								<span>
									{{ $t('form.verify-phone-text', { phone: '' }) }}
								</span>

								<span dir="ltr">
									{{ phoneNumber }}
								</span>
							</div>
							<div class="flex items-center w-full justify-center gap-2 text-sm">
								<span class="text-gray-500">
									{{ $t('form.change-otp-phone-title') }}
								</span>
								<button
									class="text-primary-500 hover:underline cursor-pointer"
									@click="() => isOtp = false"
								>
									{{ $t('form.change') }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>

		<template #footer>
			<div class="flex w-full px-4">
				<Button
					v-if="!isOtp"
					:loading="isSaving"
					class="w-full"
					@click="submitPhone"
				>
					{{ $t('form.change-phone-title') }}
				</Button>

				<Button
					v-if="isOtp"
					class="w-full"
					:loading="isSaving"
					:disabled="!isValidOTP"
					@click="submitPhoneOTP"
				>
					{{ $t('form.verify') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
