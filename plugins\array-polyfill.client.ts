/**
 * Polyfill for Array.prototype.at() method
 * Required for older browsers (Chrome < 92, Safari < 15.4, Firefox < 90)
 * This fixes Sentry web vitals compatibility issues
 */

// Only add polyfill if the method doesn't exist
if (!Array.prototype.at) {
	Array.prototype.at = function (index: number) {
		// Convert negative index to positive
		const len = this.length
		const relativeIndex = index < 0 ? len + index : index

		// Return undefined if index is out of bounds
		if (relativeIndex < 0 || relativeIndex >= len) {
			return undefined
		}

		return this[relativeIndex]
	}
}

export default defineNuxtPlugin(() => {
	// This plugin doesn't need to do anything else,
	// the polyfill is applied when the file is loaded
})
