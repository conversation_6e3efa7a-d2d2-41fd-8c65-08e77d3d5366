# EditorConfig is awesome: https://EditorConfig.org

root = true

[*]
# Set default charset to UTF-8
charset = utf-8

# Indentation Style
indent_style = tab        # Use tabs for indentation as "editor.insertSpaces" is false
indent_size = 3           # Tab size of 3 spaces

# End of Line Style
end_of_line = crlf          # LF style line endings often recommended

# Trim trailing whitespace and insert a final newline
trim_trailing_whitespace = true
insert_final_newline = true

[*.{json,jsonc,vue,ts}]
# Specific formatter per file type if supported by the editor.
indent_style = tab
indent_size = 3
