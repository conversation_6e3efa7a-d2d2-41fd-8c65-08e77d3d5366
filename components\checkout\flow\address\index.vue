<script setup lang="ts">
import { addressDefaultValues } from 'assets/seed/address'
import type { AddressResponse, Address } from '~/interfaces/auth/address'
import type { CheckoutOrder } from '~/interfaces/checkout/order'

const { order } = defineProps<{
	order?: CheckoutOrder
}>()

const emit = defineEmits<{
	(event: 'set:flow', value: number): void
}>()

const fetchKey = ref(1)
const { data, error, status } = await useApi<AddressResponse>('/addresses', {
	watch: [fetchKey],
})

if (error.value) {
	console.log('error on fetching address list', error.value)
}

const isAddNewForm = ref(false)
const addressList = computed<Address[]>(() => (data?.value as AddressResponse)?.items as Address[])
const loading = computed<boolean>(() => status?.value !== 'success')
const isSubmitting = ref<boolean>(false)
const selectedId = ref<number | null>(order?.addressId || null)
const isLocked = computed(() => order?.status !== 'draft')
const isEmptyList = computed(() => !addressList.value?.length)

const setSelectedAddress = async (): Promise<AddressResponse> => {
	const { $api } = useNuxtApp()
	return $api<unknown>(`/orders/${order.orderId}/address`, {
		method: 'POST',
		body: {
			addressId: selectedId.value,
		},
	})
}

const nextFlow = async () => {
	isSubmitting.value = true
	setSelectedAddress().then(() => {
		emit('set:flow', 2)
	})
		.finally(() => {
			isSubmitting.value = false
		})
}

const onAddressSuccess = (addressId: number) => {
	isAddNewForm.value = false
	selectedId.value = addressId
	setSelectedAddress().then(() => {
		fetchKey.value += 1
	})
}
</script>

<template>
	<CardContent class="flex-grow p-4 flex flex-col gap-4">
		<div class="flex w-full justify-end">
			<template v-if="loading">
				<Skeleton class="w-32 h-5" />
			</template>
			<template v-else>
				<Button
					v-if="!isLocked && !isEmptyList"
					variant="text"
					@click="isAddNewForm = true"
				>
					{{ $t('address.add-new-address') }}
				</Button>
			</template>
		</div>
		<div class="flex w-full flex-col gap-6">
			<template v-if="loading">
				<div class="flex flex-col w-full gap-6">
					<div
						v-for="(_, index) in Array(3)"
						:key="index"
						class="flex w-full max-w-full border rounded-lg p-4 gap-2 flex-col"
					>
						<Skeleton class="w-full h-8" />
						<Skeleton class="w-full h-5" />
						<Skeleton class="w-full h-5" />
					</div>
				</div>
			</template>
			<template v-else-if="isAddNewForm">
				<CheckoutFlowAddressForm
					:address="addressDefaultValues"
					@address:success="onAddressSuccess"
					@close:address="isAddNewForm = false"
				/>
			</template>
			<template v-else-if="isEmptyList">
				<div class="flex flex-col w-full h-full justify-center items-center gap-6">
					<Icon
						name="ui:empty-address"
						class="w-64 h-64"
					/>
					<span class="font-bold whitespace-pre text-center">{{ $t('checkout.empty-address') }}</span>
					<Button
						class="px-6"
						@click="isAddNewForm= true"
					>
						<span>{{ $t('address.add-new-address') }}</span>
					</Button>
				</div>
			</template>
			<template
				v-for="address in addressList"
				v-else
				:key="address.addressId"
			>
				<button
					:disabled="isLocked"
					class="flex border rounded-lg gap-4 items-center p-4 cursor-pointer text-start"
					:class="{ active: address.addressId === selectedId }"
					@click="selectedId = address.addressId"
				>
					<div
						class="check flex rounded-full w-5 h-5 border p-0.5"
					>
						<div class="child flex w-full h-full rounded-full" />
					</div>
					<div class="flex flex-col flex-grow gap-2 text-sm">
						<div
							v-if="address.buildingType"
							class="font-bold text-base"
						>
							{{ $t(`form.${address.buildingType}`) }}
						</div>
						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.receiver-name') }}:
							</span>
							<span class="text-gray-700">
								{{ address.recipientName || address.firstName + ' ' + (address?.lastName || '') }}
							</span>
						</div>
						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.address') }}
							</span>
							<span class="text-gray-700">
								{{
									[
										address?.city?.country?.name,
										address?.city?.name,
										address.fullAddress,
										address.district,
										address.street,
									]
										.filter(Boolean)
										.join(', ')
								}}
							</span>
						</div>

						<div class="flex w-full items-center gap-4">
							<span class="w-24 text-gray-600">
								{{ $t('form.phone') }}
							</span>
							<span
								class="text-gray-700"
								dir="ltr"
							>
								{{
									[
										'+',
										address?.phone?.code + ' ',
										address?.phone?.number,
									].join('')
								}}
							</span>
						</div>
					</div>
				</button>
			</template>
		</div>
	</CardContent>
	<CardFooter class="gap-4 justify-end">
		<Button
			:disabled="!selectedId"
			class="sm:min-w-24 xs:min-w-1/2"
			:loading="isSubmitting"
			@click.prevent="() => nextFlow()"
		>
			{{ $t("form.next") }}
		</Button>
	</CardFooter>
</template>

<style scoped lang="scss">
.active {

  @apply bg-primary-300/30 border-primary-500;
  .check {
    @apply border-primary-600;
    .child {
      @apply bg-primary-600
    }
  }

}
</style>
