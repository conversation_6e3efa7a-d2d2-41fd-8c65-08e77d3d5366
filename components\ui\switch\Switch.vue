<script setup lang="ts">
import {
	SwitchRoot,
	type SwitchRootEmits,
	type SwitchRootProps,
	SwitchThumb,
	useForwardPropsEmits,
} from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<SwitchRootProps & { class?: HTMLAttributes['class'] }>()

const emits = defineEmits<SwitchRootEmits>()

const delegatedProps = computed(() => {
	const { class: _, ...delegated } = props

	return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
</script>

<template>
	<SwitchRoot
		v-bind="forwarded"
		:direction="isRtl?'rtl':'ltr'"
		:class="cn(
			'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-600 data-[state=unchecked]:bg-gray-300 shadow',
			props.class,
		)"
	>
		<SwitchThumb
			:class="cn(`pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg transition-transform ${!isRtl?'right-0 data-[state=checked]:translate-x-5':'end-0 data-[state=checked]:-translate-x-5'}`)"
		>
			<slot name="thumb" />
		</SwitchThumb>
	</SwitchRoot>
</template>
