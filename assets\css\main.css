@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	body {
		@apply text-gray-600  bg-background text-foreground;
		font-size: 16px;
		overflow-x: hidden;
		width: 100%;
		scroll-behavior: smooth;
	}

	:root {
		--background: 0 0% 100%;
		--foreground: 0 0% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;
		--primary: 0 0% 9%;
		--primary-foreground: 0 0% 98%;
		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;
		--muted: 0 0% 96.1%;
		--muted-foreground: 0 0% 45.1%;
		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 89.8%;
		--input: 0 0% 89.8%;
		--ring: 0 0% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
	}

	html {
		scrollbar-gutter: stable;
	}
	::-webkit-scrollbar {
		@apply w-2;
	}
	::-webkit-scrollbar-thumb {
		background: linear-gradient(180deg, #9c3d8822, #732a6190);
		@apply rounded-lg w-2;
	}
	::-webkit-scrollbar-track {
		@apply bg-gray-200 overflow-hidden rounded-lg;
	}

	* {
		@apply box-border;
	}

	.card-loading {
		@apply animate-pulse bg-gray-200 rounded-lg;
	}

	.animate-pulse {
		animation: pulse 1.5s ease-in-out infinite;
	}

	@keyframes pulse {
		0% {
			background-color: rgba(209, 213, 219, 0.8);
		}
		50% {
			background-color: rgba(209, 213, 219, 0.4);
		}
		100% {
			background-color: rgba(209, 213, 219, 0.8);
		}
	}

	.truncate-2-line {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.truncate-1-line {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: horizontal;
		@apply text-nowrap overflow-hidden text-ellipsis;
	}

	.container {
		@apply w-full !max-w-[1400px] flex-1 px-0 sm:px-2;
	}

	.no-spinner::-webkit-inner-spin-button,
	.no-spinner::-webkit-outer-spin-button {
		@apply appearance-none;
	}

	.no-spinner {
		@apply [appearance:textfield]; /* For Firefox */
	}
	#nuxt-devtools-container {
		display: none;
	}
	[data-vaul-drawer][data-vaul-drawer-direction="left"]:after,
	[data-vaul-drawer][data-vaul-drawer-direction="right"]:after {
		background: transparent !important;
	}

	[data-title] {
		font-family: "Cairo", sans-serif;
		@apply text-base font-semibold;
	}
}

html body {
	/* fix flickering when menu dialog opens */
	--scrollbar-width: 0 !important;
	padding-inline: 0 !important;
}
