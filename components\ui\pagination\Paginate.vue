<script setup lang="ts">
const { itemPerPage, total, defaultPage, siblingCount, showEdges } = defineProps<{
	itemPerPage?: number
	total?: number
	defaultPage?: number
	siblingCount?: number
	showEdges?: boolean
}>()

const emit = defineEmits<{
	(event: 'update:page', page: number): void
}>()
</script>

<template>
	<div class="flex justify-center items-center w-full">
		<Pagination
			v-slot="{ page }"
			:items-per-page="itemPerPage"
			:total="total"
			:sibling-count="siblingCount"
			:show-edges="showEdges"
			:default-page="defaultPage"
			@update:page="(page) => emit('update:page', page)"
		>
			<PaginationList
				v-slot="{ items }"
				class="flex items-center gap-1"
			>
				<PaginationPrev />

				<template v-for="(item, index) in items">
					<PaginationListItem
						v-if="item.type === 'page'"
						:key="index"
						:value="item.value"
						:as-child="true"
					>
						<Button
							class="w-9 h-9"
							:variant="item.value === page ? 'default' : 'outline-secondary'"
						>
							{{ item.value }}
						</Button>
					</PaginationListItem>
					<PaginationEllipsis
						v-else
						:key="item.type"
						:index="index"
					/>
				</template>

				<PaginationNext />
			</PaginationList>
		</Pagination>
	</div>
</template>
