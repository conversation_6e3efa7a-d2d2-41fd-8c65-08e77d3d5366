/**
 * Composable for the Infinite Scroll Api
 * @param targetRef
 * @param callback
 * @param rootMargin
 * @param isIntersecting
 */
export function useInfiniteScroll(targetRef: Ref, callback: Function, rootMargin: string = '100px', isIntersecting = true) {
	const observer = ref<IntersectionObserver | null>(null)

	onMounted(() => {
		// Add safety check for ref value and ensure it's a DOM element
		if (!targetRef.value || !import.meta.client) return

		try {
			observer.value = new IntersectionObserver(
				async ([entry]) => {
					if (entry.isIntersecting && isIntersecting) {
						await callback(entry) // Execute the provided callback function
					}

					if (!isIntersecting) {
						await callback(entry)
					}
				},
				{ rootMargin, threshold: 0.01 },
			)

			observer.value.observe(targetRef.value)
		} catch (error) {
			console.warn('Failed to initialize IntersectionObserver:', error)
		}
	})

	onUnmounted(() => {
		observer.value?.disconnect()
	})

	watchEffect(() => {
		// Add additional safety checks
		if (targetRef.value && observer.value && import.meta.client) {
			try {
				observer.value.observe(targetRef.value)
			} catch (error) {
				console.warn('Failed to observe element:', error)
			}
		}
	})
}
