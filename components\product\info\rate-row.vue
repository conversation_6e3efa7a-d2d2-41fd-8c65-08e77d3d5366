<script setup lang="ts">
import type { Rating } from '~/interfaces/product/rate'

const { rate } = defineProps<{
	rate?: Rating | undefined
}>()

const color = computed(() => {
	switch (rate.rating) {
		case 1:
			return '#F36C32'
		case 2:
			return '#F36C32'
		case 3:
			return '#F3AC30'
		case 4:
			return '#82AE04'
		case 5:
		default:
			return '#38AE04'
	}
})

const iconColor = computed(() => {
	switch (rate.rating) {
		case 1:
			return '#9BD781'
		case 2:
			return '#B4CE68'
		case 3:
			return '#82AE04'
		case 4:
		case 5:
		default:
			return '#38AE04'
	}
})

const percent = computed(() => Number((rate?.percentage ?? 1) / (rate?.totalReviews ?? 1) || 0))
</script>

<template>
	<div class="rate-row flex w-full">
		<div class="flex items-center gap-2 w-full">
			<span class="text-sm font-bold">{{ rate.rating }}</span>
			<Icon
				name="ui:rate-star"
				class="icon w-3 h-3 drop-shadow-sm"
			/>
			<div class="flex w-full flex-grow h-3 bg-gray-200 rounded overflow-hidden">
				<div
					class="progress flex w- h-full  rounded"
					:style="{ width: percent + '%' }"
				/>
			</div>
			<span class="font-bold text-sm w-10 text-center">{{ percent }}%</span>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.rate-row {
  .icon {
    color: v-bind(iconColor);
  }

  .progress {
    background-color: v-bind(color);
  }
}
</style>
