export interface CheckoutOrder {
	orderId?: number
	status?: string
	paymentStatus?: string
	paymentMethodId?: number
	createdAt?: string
	updatedAt?: string
	userId?: number
	addressId?: number
	total?: Total
	subTotal?: SubTotal
	shippingPrice?: ShippingPrice
	tax?: unknown
	visitorId?: unknown
	shippingCarrierId?: number
	deletedAt?: unknown
	orderItems?: OrderItems[]
	user?: User
	address?: Address
	shippingCarrier?: ShippingCarrier
	paymentMethod?: PaymentMethod
}

export interface Total {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface SubTotal {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface ShippingPrice {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface User {
	userId?: number
	firstName?: string
	lastName?: string
	email?: string
	emailVerifiedAt?: unknown
	birthday?: unknown
	status?: string
	createdAt?: string
	updatedAt?: string
	phone?: Phone
	gender?: string
	fullName?: string
	wallet?: Wallet
	media?: Media
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
}

export interface Wallet {
	value?: number
	currency?: string
	symbol?: string
}

export interface Media {
	mediaId?: unknown
	src?: unknown
}

export interface OrderItems {
	orderItemsId?: number
	orderId?: number
	model_type?: string
	model_id?: number
	snapshot?: Snapshot
	productOrderItemId?: number
	productId?: number
	varianceId?: number
	brandId?: unknown
	quantity?: number
	status?: string
	discount?: number
	stockId?: number
	product?: Product
	variance?: Variance
	price?: Price
	originalPrice?: OriginalPrice
}

export interface Snapshot {
	media?: Media[]
	product?: Product
	variance?: Variance
	activeStock?: ActiveStock
	attributesWithValue?: AttributesWithValue[]
}

export interface Product {
	name?: string
	slug?: string
	type?: string
	colors?: unknown
	avgRate?: number
	brandId?: number
	oldSlug?: string
	hasOffer?: boolean
	hasStock?: boolean
	isListed?: boolean
	maxPrice?: number
	minPrice?: number
	priority?: number
	createdAt?: string
	deletedAt?: unknown
	metaTitle?: string
	productId?: number
	releaseAt?: unknown
	updatedAt?: string
	shippingId?: unknown
	description?: string
	isPublished?: boolean
	publishedAt?: unknown
	numberOfOrder?: number
	unPublishedAt?: unknown
	productGroupId?: unknown
	metaDescription?: string
	maxPriceBeforeOffer?: number
	minPriceBeforeOffer?: number
	variationAttributes?: string
}

export interface Variance {
	SKU?: unknown
	name?: string
	slug?: string
	type?: string
	brandId?: number
	oldSlug?: string
	auctionId?: unknown
	createdAt?: string
	deletedAt?: unknown
	isDefault?: boolean
	metaTitle?: string
	productId?: number
	updatedAt?: string
	varianceId?: number
	activeStock?: ActiveStock
	isPublished?: number
	modelNumber?: unknown
	publishedAt?: unknown
	unPublishedAt?: unknown
	metaDescription?: string
}

export interface ActiveStock {
	cost?: number
	note?: string
	sold?: number
	sort?: number
	price?: number
	isOffer?: boolean
	stockId?: number
	quantity?: number
	createdAt?: string
	updatedAt?: string
	isPreOrder?: boolean
	maxPerUser?: number
	supplierId?: number
	isPublished?: boolean
	publishedAt?: string
	unPublishedAt?: string
	priceBeforeOffer?: number
	laravel_through_key?: number
}

export interface Media {
	id?: number
	alt?: unknown
	disk?: string
	name?: string
	size?: number
	sort?: number
	uuid?: string
	model_id?: number
	file_name?: string
	mime_type?: string
	created_at?: string
	model_type?: string
	updated_at?: string
	isPublished?: number
	preview_url?: string
	publishedAt?: unknown
	order_column?: number
	original_url?: string
	unPublishedAt?: unknown
	collection_name?: string
	thumbnailBase64?: unknown
	conversions_disk?: string
}

export interface AttributesWithValue {
	key?: string
	name?: string
	slug?: string
	type?: string
	extra?: Extra
	value?: unknown
	number?: unknown
	prefix?: string
	suffix?: string
	hexCode?: string
	isColor?: boolean
	createdAt?: string
	hasFilter?: boolean
	productId?: unknown
	updatedAt?: string
	isRequired?: boolean
	varianceId?: number
	attributeId?: number
	option_name?: string
	option_slug?: string
	usedProductId?: unknown
	attributeOptionId?: number
	attributeValuesId?: number
}

export interface Extra {
	en?: unknown
}

export interface Product {
	productId?: number
	name?: string
	brandId?: number
	productGroupId?: unknown
	description?: string
	type?: string
	slug?: string
	isListed?: boolean
	variationAttributes?: string
	avgRate?: number
	media?: Media
}

export interface Media {
	gallery?: Gallery[]
	cover?: Cover[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}

export interface Cover {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}

export interface Variance {
	varianceId?: number
	auctionId?: unknown
	name?: string
	slug?: string
	brandId?: number
	SKU?: unknown
	type?: string
	stock?: Stock
}

export interface Stock {
	stockId?: number
	quantity?: number
	maxPerUser?: number
	supplierId?: number
	isOffer?: boolean
	priceBeforeOffer?: PriceBeforeOffer
	price?: Price
	unPublishedAt?: string
	isPreOrder?: boolean
	note?: string
}

export interface PriceBeforeOffer {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Price {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface OriginalPrice {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Address {
	addressId?: number
	cityId?: number
	district?: string
	createdAt?: string
	updatedAt?: string
	userId?: number
	phone?: Phone
	street?: string
	recipientName?: string
	apartmentNumber?: string
	buildingNumber?: string
	default?: number
	visitorId?: unknown
	firstName?: string
	lastName?: string
	email?: unknown
	buildingType?: string
	fullAddress?: unknown
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
}

export interface ShippingCarrier {
	shippingCarrierId?: number
	name?: string
	slug?: string
	phone?: Phone
	label?: string
	haveFastShipping?: boolean
	default?: boolean
	createdAt?: string
	updatedAt?: string
}

export interface PaymentMethod {
	paymentMethodId?: number
	name?: string
	module?: string
	createdAt?: string
	updatedAt?: string
	status?: string
	allowFillWallet?: number
	media?: Media
}

export interface Media {
	logo?: Logo
}

export interface Logo {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}
