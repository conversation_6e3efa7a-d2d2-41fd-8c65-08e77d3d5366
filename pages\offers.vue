<script setup lang="ts">
import { useMediaQuery, useWindowSize } from '@vueuse/core'
import { useInfiniteScroll } from '~/composables/useInfiniteScroll'
import type { ProductList, Item, Pagination, Filters as CategoryFilters } from '~/interfaces/product/product-list'

const { height: screenHeight } = useWindowSize()
const PER_PAGE = 24
const router = useRouter()
const route = useRoute()
const products = ref<Item[]>([])
const loadingMore = ref<boolean>(false)
const loadMoreBtnRef = ref<HTMLButtonElement | null>(null)
const isDesktop = useMediaQuery('(min-width: 600px)', { ssrWidth: 1000 })
const config = useRuntimeConfig()

const { t } = useI18n()

type QueryType = {
	page?: string
}
const query = computed<QueryType>(() => route.query)
if (query.value?.page && Number(query.value?.page) <= 1) {
	navigateTo(route.path, { redirectCode: 301 })
}
/** Selected filters **/
const selectedFilters = ref<QueryType>(query.value)
const page = computed<number>(() => Number(query.value?.page || 1))
const apiQuery = computed(() => {
	return {
		orderBy: 'createdAt,desc',
		perPage: PER_PAGE,
		page: query.value?.['page'] || 1,
		...selectedFilters.value,
	}
})

/** fetch product api **/
const { data, error, status } = await useApi<ProductList>(`/offers`, {
	query: apiQuery,
	watch: [apiQuery],
})

/** In case of any error **/
if (error.value) {
	if (error.value.statusCode === 404) {
		throw createError({
			statusCode: 404,
		})
	}
	console.error(`Error fetching offers products:`, error.value)
}

const result = computed(() => data.value as ProductList)
const pagination = computed(() => (data.value as ProductList)?.pagination as Pagination)

if (!import.meta.client) {
	products.value = (result.value as ProductList)?.items as Item[]
}
/** Watching on the data result **/
watch(data, () => {
	if (!result.value?.items || !import.meta.client) {
		return
	}

	if (!page.value || page.value == 1) {
		products.value = result.value.items as Item[] || []
	} else {
		products.value = [...(products.value || []), ...(result.value.items || [])] as Item[]
	}
	loadingMore.value = false
}, { immediate: true })

watch(query, (dataQuery) => {
	selectedFilters.value = dataQuery || selectedFilters.value
}, { immediate: true, deep: true })

/** Loading enabled only on the open page **/
const loading = computed<boolean>(() => status.value === 'pending')

/** Disable Load More Btn **/
const disableLoadMore = computed<boolean>(() => {
	if (isEmptyState.value) {
		return true
	}
	return pagination.value?.lastPage <= Number(page.value)
})

/** Show Empty State **/
const isEmptyState = computed<boolean>(() => {
	if (loading.value) {
		return false
	}

	return !products.value?.length
})

/** Next page link */
const nextPagePath = computed(() => {
	const query = new URLSearchParams({
		...route.query,
		page: String(Number(route.query?.page ?? 1) + 1),
	}).toString()

	return `${route.path}?${query}`
})

const totalProducts = computed<number>(() => result.value?.pagination?.total || 0)
const filter = computed(() => result.value?.filters as CategoryFilters)

/**
 * Update the filter values accumulation
 * @param selected
 */
const onFilterUpdate = (selected: Record<string, string | string[] | null | undefined>) => {
	products.value = []
	status.value = 'pending'

	const query = route.query

	nextTick(() => {
		const filteredQuery = Object.fromEntries(
			Object.entries({
				...query,
				...selected,
				page: 1,
			}).filter(([_, v]) => Boolean(v)),
		)

		router.push({
			path: route.path,
			query: filteredQuery,
		})
	})
}

/** Special loading status for the filters only **/
const pageLoading = computed(() => !!loading.value && !filter.value)

/** Card only loading in case there is loading and no products in a list **/
const cardLoading = computed(() => !!loading.value && !products.value?.length)

/** Get the root margin by 3dvh **/
const rootMargin = computed(() => ((screenHeight.value * 30) / 100) + 'px')

/** load more on infinite scroll */
useInfiniteScroll(loadMoreBtnRef, async () => {
	if (!disableLoadMore.value) {
		loadingMore.value = true
		await navigateTo(nextPagePath.value)
	}
}, rootMargin.value)

const pageNumber = computed(() => page.value > 1 ? `- ${t(`form.page-number`, { page: page.value })}` : '')
const metaTitle = computed(() => `${t('offer.meta-title')} ${pageNumber.value ?? ''}`)
const metaDescription = computed(() => `${t('offer.meta-description')} ${pageNumber.value ?? ''}`)
const siteUrl = computed(() => `${config?.public?.siteUrl}${route?.path}${page.value > 1 ? `?page=${page.value}` : ''}`)

useSeoMeta({
	title: metaTitle,
	description: () => metaDescription.value,
	ogTitle: () => metaTitle.value,
	ogDescription: () => metaDescription.value,
	twitterTitle: () => metaTitle.value,
	twitterDescription: () => metaDescription.value,
})

useHead({
	link: [
		{ rel: 'canonical', href: siteUrl.value },
		{ rel: 'twitter:card', href: siteUrl.value },
		{ rel: 'og:url', href: siteUrl.value },
	],

	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<div class="flex-col flex gap-2">
		<Card class="flex flex-col">
			<CardHeader class="flex flex-col max-sm:!px-4">
				<div class="flex w-full justify-between items-center">
					<h1 class="text-md font-bold">
						{{ $t('offer.title') }}
					</h1>
					<div class="flex">
						<FilterSorting
							v-if="isDesktop"
							:total="totalProducts"
							:update="onFilterUpdate"
							:loading="pageLoading"
						/>
						<FilterMobile
							v-else
							:total="totalProducts"
							:filter="filter"
							:loading="pageLoading"
							:update="onFilterUpdate"
						/>
					</div>
				</div>
			</CardHeader>
			<CardContent class="grid gap-4 pb-6 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-5 xs:px-4">
				<ProductCard
					v-for="(product, index) in products"
					:key="product.productId"
					:product="product"
					:image-loading="index >= 8 ? 'lazy' : 'eager'"
				>
					<template #title="{ product }">
						<h2>{{ product?.name }}</h2>
					</template>
				</ProductCard>

				<template v-if="!!cardLoading">
					<ProductCard
						v-for="(_, index) in Array(PER_PAGE)"
						:key="`product.${_}${index}`"
						:loading="true"
					/>
				</template>

				<template v-if="isEmptyState">
					<div class="col-span-4 flex flex-col gap-4 justify-center items-center py-12">
						<Icon
							name="ui:empty-search"
							class="h-80 w-80"
						/>
						<span class="text-lg font-bold">{{ $t('offer.empty-search-text') }}</span>
					</div>
				</template>
			</CardContent>
			<CardFooter
				v-if="!disableLoadMore"
				class="flex items-center justify-center p-5"
			>
				<Button
					v-if="products?.length"
					variant="outline"
					:size="'lg'"
					:as-child="true"
					class="[&_svg]:size-10"
				>
					<NuxtLink
						:disabled="loadingMore"
						:to="nextPagePath"
					>
						<template v-if="!loadingMore">
							{{ $t('categories.more-btn-title') }}
						</template>
						<template v-else>
							<Spinner />
						</template>
					</NuxtLink>
					<span />
				</Button>
			</CardFooter>
			<div ref="loadMoreBtnRef" />
		</Card>
	</div>
</template>
