<script setup lang="ts">
import { useEventListener } from '@vueuse/core'

const links = [
	{
		title: 'mobile.nav-home-title',
		icon: 'lucide:house',
		path: '/',
	},
	{
		title: 'mobile.nav-categories-title',
		icon: 'lucide:layout-grid',
		path: '/category/all',
	},
	{
		title: 'mobile.nav-search-title',
		icon: 'lucide:search',
		path: '/search?q=',
	},

	{
		title: 'mobile.nav-cart-title',
		icon: 'lucide:shopping-cart',
		path: '/cart',
	},

	{
		title: 'mobile.nav-profile-title',
		icon: 'lucide:circle-user-round',
		path: 'my/profile',
	},
]

const showBottom = ref<boolean>(true)
let lastScrollY: number = 0

/** handel on scroll hide the top header */
const handleScroll = (): void => {
	showBottom.value = window.scrollY < lastScrollY
	lastScrollY = window.scrollY as number
}

/** Event listener **/
useEventListener('scroll', handleScroll)
</script>

<template>
	<div
		class="w-full h-20 hidden transition-all max-sm:flex delay-75"
		:class="{ 'translate-y-20 h-4': !showBottom }"
	>
		<div class="fixed bottom-0 z-20 min-w-full">
			<div class="flex justify-evenly items-center px-2 pb-4 border-t w-full border-gray-200  bg-white text-gray-400">
				<NuxtLinkLocale
					v-for="link in links"
					:key="link.title"
					class="min-h-9 flex flex-col items-center border-t-[3px] border-transparent justify-center px-2 gap-1 pt-2 -top-px relative"
					:to="link.path"
					exact-active-class="text-primary-600 !border-primary-600"
				>
					<div class="flex h-8">
						<Icon
							:name="link.icon"
							size="25px"
						/>
					</div>
					<span class="text-sm">{{ $t(link.title) }}</span>
				</NuxtLinkLocale>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
