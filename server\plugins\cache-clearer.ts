import { joinURL } from 'ufo'
import { defineNitroPlugin, useStorage } from 'nitropack/runtime'

export default defineNitroPlugin(async (_nitroApp) => {
	// // Skip in development
	// if (process.env.NODE_ENV !== 'production') {
	// 	return
	// }

	// if (process.env.ENABLE_CACHE !== 'true') {
	// 	console.log('Cache is not enabled')
	// 	return
	// }

	// if (!process.env.CACHE_TOKEN) {
	// 	console.log('process.env.CACHE_TOKEN: ' + process.env.CACHE_TOKEN)
	// 	console.log('Cache token not provided, skipping cache clear')
	// 	return
	// }

	// if (!process.env.APP_URL) {
	// 	console.log('App base url not provided, skipping cache clear')
	// 	return
	// }

	// // Use Nitro's storage to check if we've cleared cache for this build
	// const storage = useStorage('cache')
	// const buildId = process.env.BUILD_ID || Date.now().toString()
	// const cacheKey = `cache_cleared_${buildId}`

	// // Check if we've already cleared for this build
	// const alreadyCleared = await storage.hasItem(cacheKey)
	// if (alreadyCleared) {
	// 	console.log('Cache already cleared for this build')
	// 	return
	// }

	// console.log('🧹 Clearing cache on first server startup after build...')
	// const url = joinURL(process.env.APP_URL, '/__nuxt_multi_cache/purge/all')
	// setTimeout(async () => {
	// 	try {
	// 		const response = await fetch(url, {
	// 			method: 'POST',
	// 			headers: {
	// 				'Content-Type': 'application/json',
	// 				'x-nuxt-multi-cache-token': process.env.CACHE_TOKEN as string,
	// 			},
	// 		})
	// 		const result = await response.json()
	// 		console.log('Cache clear result:', result)

	// 		// Mark as cleared for this build
	// 		await storage.setItem(cacheKey, 'true')
	// 	} catch (error) {
	// 		console.error('Failed to clear cache:', error)
	// 		console.log('URL:', url)
	// 	}
	// }, 1000)
})
