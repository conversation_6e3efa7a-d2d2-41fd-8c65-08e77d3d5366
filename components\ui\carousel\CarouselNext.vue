<script setup lang="ts">
import type { WithClassAsProps } from './interface'
import { useCarousel } from './useCarousel'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

const props = defineProps<WithClassAsProps>()

const { orientation, canScrollNext, scrollNext, ...reset } = useCarousel()
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
if (import.meta.client) {
	window.scroll = { ...reset }
}
</script>

<template>
	<Button
		:disabled="!canScrollNext"
		:class="cn(
			'touch-manipulation absolute h-8 w-8 p-0 shadow',
			orientation === 'horizontal'
				? 'right-0 top-1/2 -translate-y-1/2'
				: '-bottom-12 left-1/2 -translate-x-1/2 rotate-90',

			isRtl ? 'rotate-180 right-auto left-0' : '',
			props.class,
		)"
		variant="white"
		@click="scrollNext"
	>
		<slot>
			<Icon
				name="lucide:chevron-right"
				class="h-4 w-4 text-current"
			/>
			<span class="sr-only">Next Slide</span>
		</slot>
	</Button>
</template>
