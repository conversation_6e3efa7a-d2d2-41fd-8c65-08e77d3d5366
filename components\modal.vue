<script lang="ts" setup>
import { useMediaQuery, createReusableTemplate } from '@vueuse/core'
import { DialogClose } from 'reka-ui'

const isDesktop = useMediaQuery('(min-width: 600px)')
const [UseBody, BodyGrid] = createReusableTemplate()
const [UseFooter, FooterGrid] = createReusableTemplate()
const { title, description, dismissible, size = 'max-w-lg' } = defineProps<{
	title?: string
	description?: string
	dismissible?: boolean
	hideClose?: boolean
	size?: string | 'max-w-2xl' | 'max-w-xl' | 'max-w-lg' | 'max-w-md' | 'max-w-sm' | 'max-w-xs'
}>()

const emit = defineEmits<{
	(event: 'close'): void
}>()

const handleOnUpdate = (isOpen: boolean = false): void => {
	if (!isOpen) {
		emit('close')
	}
}

const handleOnDrag = (sensitivity: number = 0): void => {
	if (sensitivity >= 0.4) {
		emit('close')
	}
}
</script>

<template>
	<UseBody>
		<slot name="body" />
	</UseBody>

	<UseFooter>
		<slot name="footer" />
	</UseFooter>

	<template v-if="isDesktop">
		<Dialog
			default-open
			@update:open="handleOnUpdate"
		>
			<DialogContent
				:class="[size]"
				:hide-close="hideClose"
			>
				<DialogHeader :class="{ hidden: !title }">
					<DialogTitle>
						{{ title || 'Dialog title' }}
					</DialogTitle>
				</DialogHeader>
				<DialogDescription :class="{ hidden: !description }">
					{{ description || 'Dialog description' }}
				</DialogDescription>
				<div class="max-h-[65vh] min-h-24 h-full overflow-y-auto">
					<BodyGrid />
				</div>

				<DialogFooter>
					<FooterGrid />
				</DialogFooter>
			</DialogContent>
		</Dialog>
	</template>
	<template v-if="!isDesktop">
		<Drawer
			:default-open="true"
			:dismissible="dismissible"
			@update:open="handleOnUpdate"
			@drag="handleOnDrag"
		>
			<DrawerContent class="pt-0">
				<DialogClose
					v-if="!hideClose"
					class="absolute end-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground !outline-none"
					@click="emit('close')"
				>
					<Icon
						name="lucide:x-circle"
						class="w-7 h-7 text-gray-600"
					/>
					<span class="sr-only">Close</span>
				</DialogClose>
				<DrawerHeader
					v-if="title"
					class="border-b border-gray-200"
				>
					<DrawerTitle v-if="title">
						{{ title }}
					</DrawerTitle>
				</DrawerHeader>
				<DrawerDescription
					v-if="description"
					class="p-4"
				>
					{{ description }}
				</DrawerDescription>

				<BodyGrid />

				<DrawerFooter class="pt-2">
					<FooterGrid />
				</DrawerFooter>
			</DrawerContent>
		</Drawer>
	</template>
</template>
