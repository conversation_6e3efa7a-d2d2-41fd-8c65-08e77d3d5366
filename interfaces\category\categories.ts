export interface Categories {
	cover: Cover
	children: Categories[]
	metaTitle: string
	name: string
	metaDescription: string
	isShowBrandIngListing: boolean
	categoryId: number
	slug: string
	parentId?: number
	value?: string | number
}

export interface Cover {
	preview: string
	disk: Disk
	src: string
	fileSize: number
	id: number
	mimeType: MIMEType
	sort: number
}

export enum Disk {
	S3 = 's3',
}

export enum MIMEType {
	ImagePNG = 'image/png',
	ImageSVGXML = 'image/svg+xml',
	ImageWebp = 'image/webp',
}
