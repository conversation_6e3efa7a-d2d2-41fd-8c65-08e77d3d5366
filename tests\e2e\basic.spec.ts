import { test, expect } from '@playwright/test'
import { testI18n as i18n } from './helpers/i18n-helper'

test.describe('Basic App Functionality', () => {
	// Set timeout for all tests in this describe block
	test.setTimeout(120000) // 2 minutes

	test('should load homepage successfully', async ({ page }) => {
		await page.goto('/', { timeout: 60000 }) // 60 seconds timeout

		// Check if the page loads
		await expect(page).toHaveTitle(`${i18n.t('header.meta-site-name')} | ${i18n.t('header.meta-title')}`)

		// Check if main navigation is visible
		await expect(page.locator('header')).toBeVisible()

		// Check if the page contains some expected content
		await expect(page.locator('body')).toContainText(/action/i)
	})

	test('should navigate to login modal via URL', async ({ page }) => {
		await page.goto('/?auth=login', { timeout: 60000 }) // 60 seconds timeout

		// Check if login modal appears
		await expect(page.locator('[role="dialog"]')).toBeVisible({
			timeout: 10 * 1000,
		})

		// Check if phone and password inputs are present
		await expect(page.locator('[role="dialog"] .base-phone-input')).toBeVisible()
		await expect(page.locator('[role="dialog"] input[type="password"]')).toBeVisible()
	})

	test('should be responsive on mobile', async ({ page }) => {
		// Set mobile viewport
		await page.setViewportSize({ width: 375, height: 667 })
		await page.goto('/', { timeout: 60000 }) // 60 seconds timeout

		// Check if the page loads on mobile
		await expect(page.locator('header')).toBeVisible()

		// Check if mobile-specific elements are present
		await expect(page.locator('body')).toBeVisible()
	})
})
