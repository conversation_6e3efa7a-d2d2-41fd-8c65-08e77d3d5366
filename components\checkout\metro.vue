<script setup lang="ts">
const route = useRoute()

const { loading, isLocked } = defineProps<{
	loading?: boolean
	isLocked?: boolean
}>()

const list = [
	{
		title: 'checkout.contact-title',
		text: 'checkout.contact-text',
		step: 1,
	},
	{
		title: 'checkout.shipping-title',
		text: 'checkout.shipping-text',
		step: 2,
	},
	{
		title: 'checkout.pay-title',
		text: 'checkout.pay-text',
		step: 3,
	},
	{
		title: 'checkout.review-title',
		text: 'checkout.review-text',
		step: 4,
	},
]

const step = computed(() => route.params?.stepId)
const stepIndex = computed(() => Number(step.value) - 1)
</script>

<template>
	<Card>
		<CardHeader>
			<div class="relative grid grid-cols-4 gap-2 z-0">
				<template v-if="loading">
					<div
						v-for="(_, index) in Array(4)"
						:key="index"
						class="flex col gap-2 items-center z-10 bg-white"
					>
						<Skeleton class="w-8 h-8 rounded-full" />
						<div class="flex flex-col gap-2 flex-grow">
							<Skeleton class="w-[60%] h-4" />
							<Skeleton class="w-[60%] h-4" />
						</div>
					</div>
				</template>
				<template v-else>
					<button
						v-for="(item, index) in list"
						:key="index"
						class="flex z-10 md:text-start max-sm:justify-center md:justify-normal"
					>
						<div class="flex p-2 gap-2 items-center justify-center bg-white max-md:flex-col max-md:justify-start">
							<div
								class="flex rounded-full border-2 border-transparent p-1"
								:class="{ '!border-primary-600': index === stepIndex }"
							>
								<div
									class="flex p-2 bg-white border-2 border-gray-300 justify-center items-center rounded-full w-7 h-7"
									:class="{ '!bg-primary-600 border-none': index <= stepIndex || isLocked }"
								>
									<template v-if="index < stepIndex || isLocked ">
										<Icon
											name="lucide:check"
											size="16px"
											class="text-white"
										/>
									</template>
									<template v-else>
										<span
											class="text-sm"
											:class="{ 'text-white': index === stepIndex }"
										>
											{{ 1 + index }}
										</span>
									</template>
								</div>
							</div>
							<div class="flex flex-col gap-1 justify-center max-w-28 max-sm:hidden">
								<span class="text-sm font-semibold text-gray-600">
									{{ $t(item.title) }}
								</span>

								<span class="text-xs font-medium text-gray-500">
									{{ $t(item.text) }}
								</span>
							</div>
						</div>
					</button>
				</template>
				<div class="flex  border-t-2 border-gray-200 flex-grow absolute top-1/2 bottom-1/2 left-[10%] right-[10%]" />
			</div>
		</CardHeader>
	</Card>
</template>

<style scoped lang="scss">

</style>
