<script setup lang="ts">
import type { CheckoutOrder } from '~/interfaces/checkout/order'

const { t } = useI18n()
const route = useRoute()

const orderId = computed(() => route.params?.orderId)
const { data, error, status } = await useApi<CheckoutOrder>(`/orders/${orderId.value}`, {
	watch: [orderId],
})

if (error.value) {
	console.log('Error on fetching checkout order', error.value)
}

const loading = computed<boolean>(() => status.value !== 'success')
const order = computed<CheckoutOrder>(() => data.value as CheckoutOrder)
const isLocked = computed(() => order.value?.status !== 'draft')

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	const { buildSinglePage } = useBreadcrumbs()
	return buildSinglePage(t('checkout.title'))
})

definePageMeta({
	ssr: false,
	validate: async (route) => {
		return ['1', '2', '3', '4'].includes(route.params?.stepId as string)
	},
})

/** Set SEO Data **/
const { setSeoData } = useMetaData({
	pageName: t('checkout.title'),
	fullPath: route?.fullPath,
	title: t('checkout.title'),
	metaTitle: t('checkout.title'),
})
setSeoData()
</script>

<template>
	<div class="flex flex-col w-full gap-6">
		<Breadcrumb :links="breadCrumbLinks" />
		<div class="flex justify-between items-start gap-6">
			<div class="flex flex-col gap-6 sm:w-2/3 xs:w-full">
				<CheckoutMetro
					:loading="loading"
					:is-locked="isLocked"
				/>

				<CheckoutFlow
					:loading="loading"
					:order="order"
					:is-locked="isLocked"
				/>
			</div>
			<div class="sm:flex flex-col sm:w-1/3 xs:hidden">
				<CheckoutProducts
					:loading="loading"
					:order="order"
				/>
			</div>
		</div>
	</div>
</template>
