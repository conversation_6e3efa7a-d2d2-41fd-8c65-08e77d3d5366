<script setup lang="ts">
const { t, locale } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('header.top-about'))
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
const isRtl = computed(() => locale.value === 'ar')

useSeoMeta({
	title: () => `${t('about-us.title')} | ${t('header.meta-site-name')}`,
	description: () => t('about-us.meta-description'),
	ogTitle: () => `${t('about-us.title')} | ${t('header.meta-site-name')}`,
	ogDescription: () => t('about-us.meta-description'),
	twitterTitle: () => `${t('about-us.title')} | ${t('header.meta-site-name')}`,
	twitterDescription: () => t('about-us.meta-description'),
})
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="relative text-center justify-center gap-4 text-white  rounded-lg !p-0">
			<NuxtImg
				src="/images/about/lg.png"
				sizes="1765px"
				width="1765"
				height="537"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden lg:block w-full"
				alt="Large screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/about/md.png"
				sizes="991px"
				width="991"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden md:block lg:hidden w-full"
				alt="Medium screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/about/sm.png"
				sizes="398px"
				width="398"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover block md:hidden w-full"
				alt="Small screen image"
				:preload="true"
			/>
			<div class="absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0">
				<h1 class="font-bold text-2xl max-sm:text-sm">
					{{ $t('about-us.title') }}
				</h1>
				<h2 class="font-bold text-xl max-sm:text-xs">
					{{ $t('about-us.sub-title') }}
				</h2>
				<p class="text-base max-w-[75%] text-center max-sm:text-2xs max-sm:leading-tight">
					{{ $t('about-us.title-paragraph') }}
				</p>
			</div>
		</CardHeader>
		<CardContent class="flex flex-col gap-12 py-12">
			<div class="flex flex-col p-4 rounded-lg bg-sky-50 text-gray-600">
				<span class="text-lg font-bold">{{ $t('about-us.welcome-1') }}</span>
				<span class="text-base">{{ $t('about-us.welcome-2') }}</span>
			</div>

			<div class="flex flex-col">
				<span class="text-lg font-bold">{{ $t('about-us.story-title') }}</span>
				<span class="text-md">{{ $t('about-us.story-description') }}</span>
			</div>

			<div class="flex justify-between items-center gap-6 max-sm:flex-col">
				<div class="flex flex-col w-1/2 max-sm:w-full gap-2">
					<NuxtImg
						src="images/about-1.png"
						format="webp"
					/>
					<NuxtLinkLocale
						to="vision"
						class="flex items-center gap-2 text-sm text-gray-500 hover:text-primary-500  cursor-pointer underline"
					>
						{{ $t('about-us.vision-title') }}
						<Icon
							name="lucide:chevron-right"
							:class="{ 'rotate-180': isRtl }"
							size="15px"
						/>
					</NuxtLinkLocale>
				</div>
				<div class="flex flex-col w-1/2 max-sm:w-full gap-2">
					<NuxtImg
						src="images/about-2.png"
						format="webp"
					/>
					<NuxtLinkLocale
						to="services"
						class="flex items-center gap-2 text-sm text-gray-500  hover:text-primary-500  cursor-pointer underline"
					>
						{{ $t('about-us.service-title') }}
						<Icon
							name="lucide:chevron-right"
							:class="{ 'rotate-180': isRtl }"
							size="15px"
						/>
					</NuxtLinkLocale>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
