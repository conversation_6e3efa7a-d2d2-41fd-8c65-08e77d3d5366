<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'
import Modal from '../modal.vue'
import type FormPhoneValue from '~/interfaces/form'
import type { AuthLoginPayload } from '~/interfaces/auth/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()

const router = useRouter()
const route = useRoute()
const { t } = useI18n()

const passwordEye = ref(false)
const submitting = ref(false)

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			password: z.string()
				.min(7, t('error.required')),

			phone: z.object({
				number: z.string().optional(),
				iso: z.string().optional(),
				code: z.string().optional(),
				isValid: z.boolean().optional(),
			})
				.refine(phone => phone.isValid, t('error.phone-number-invalid')),
		}),
	),

	initialValues: toRaw({
		password: '',
		phone: {
			code: '962',
			iso: 'JO',
			number: '',
			isValid: true,
		},
	}),
})

/** Close login dialog **/
const onCloseLogin = () => {
	const query = { ...route.query }
	delete query.auth
	return router.push({
		path: route.path,
		query,
	})
}

/** Handle on login user **/
const onLogin = Form.handleSubmit(async (values) => {
	submitting.value = true
	await authStore.login({
		phone: values.phone,
		password: values.password,
	} as AuthLoginPayload)
		.then(() => {
			onCloseLogin()
		})
		.catch(() => {
			Form.setErrors({
				phone: t('error.invalid-username-password'),
				password: '',
			})
		})

	return nextTick(() => {
		nextTick(() => submitting.value = false)
	})
})

const isFormValid = computed(() => Form.isFieldValid('phone') && Form.isFieldValid('passwowrd'))
</script>

<template>
	<Modal
		:dismissible="true"
		size="!p-0"
		:hide-close="false"
		@close="onCloseLogin"
	>
		<template #body>
			<div class="flex flex-col w-full gap-4">
				<div class="bg-gray-200 h-56 flex items-center justify-center rounded-t-lg">
					<NuxtImg
						src="/images/logo.png"
						:alt="$t('app.action-mobile-logo')"
						:title="$t('app.action-mobile-logo')"
						class="h-28"
						loading="lazy"
					/>
				</div>

				<div class="relative col-span-2 flex flex-col w-full gap-2  px-4">
					<FormPhone
						:error="Form.errors?.value?.phone"
						@update="(value: FormPhoneValue) => {
							Form.setFieldValue('phone', {
								number: value.nationalNumber,
								code: value.countryCallingCode,
								iso: value.countryCode,
								isValid: value.isValid,
							})
						}"
					/>
				</div>
				<div class="col-span-2 w-full px-4">
					<FormField
						v-slot="{ componentField }"
						name="password"
					>
						<FormItem class="relative">
							<FormLabel class="font-semibold">
								{{ $t('form.password') }}*
							</FormLabel>
							<FormControl>
								<Input
									:type="!passwordEye?'password':'text'"
									:placeholder="$t('form.password')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
							<div class="flex absolute top-8 end-4">
								<button
									class="p-1"
									@click="() => passwordEye = !passwordEye"
								>
									<Icon
										:name="passwordEye?'lucide:eye':'lucide:eye-off'"
										size="18px"
									/>
								</button>
							</div>
						</FormItem>
					</FormField>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="flex w-full flex-col gap-2 py-4">
				<div class="flex justify-end">
					<NuxtLink
						:to="`${route.path}?auth=forgot-password`"
						class="hover:underline hover:text-primary-500 text-base text-gray-500"
					>
						{{ $t("auth.forget-password-question") }}
					</NuxtLink>
				</div>

				<div class="flex flex-col gap-4">
					<Button
						class="w-full"
						:loading="submitting"
						:disabled="!isFormValid"
						@click.prevent="onLogin"
					>
						{{ $t('auth.log-in') }}
					</Button>

					<div class="flex flex-col py-2 justify-center items-center relative w-full ">
						<span class="bg-white px-2 z-10 inline text-xs text-gray-500">
							{{ $t('auth.new-gust-hint') }}
						</span>
						<div class="flex w-full bg-gray-200 absolute my-4 left-0 h-px z-0" />
					</div>

					<Button
						variant="outline"
						class="w-full"
						:as-child="true"
					>
						<NuxtLink :to="`${route.path}?auth=signup`">
							{{ $t('auth.sign-up') }}
						</NuxtLink>
					</Button>
				</div>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
