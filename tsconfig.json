{
  // https://nuxt.com/docs/guide/concepts/typescript

  "extends": "./.nuxt/tsconfig.json",

//  "compilerOptions": {
//    "resolveJsonModule": true,
//    "esModuleInterop": true,
//    "strict": false,
//    "module": "esnext",
//    "moduleResolution": "node",
//    "lib": ["esnext", "dom"],
//    "types": [
//      "node",
//      "@types/node",
//      "@nuxt/types",
//      "vue-i18n"
//    ],
//  },
//  "include": [
//    "types/vue-shims.d.ts",
//    "**/*.ts",
//    "**/*.tsx",
//    "**/*.vue"
//  ],
//  "exclude": [
//    "nuxt.config.ts",
//    "node_modules/*",
//    ".nuxt",
//    "dist",
//	 "components/ui"
//  ]
}