<script setup lang="ts">
const { status, size } = defineProps<{
	status?: string
	size?: string
}>()

// draft canceled completed refunded received
</script>

<template>
	<Badge :class="`wallet-status ${status} ${size}`">
		{{ $t(`wallet.status-${status}`) }}
	</Badge>
</template>

<style scoped lang="scss">
.wallet-status {
  &.sm{
    @apply text-2xs px-3;
  }
  &.in-progress, &.pending {
    @apply bg-orange-400 text-white;
  }

  &.success, &.completed {
    @apply bg-green-1000 text-white;
  }

  &.rejected {
    @apply bg-rose-600 text-white;
  }

}
</style>
