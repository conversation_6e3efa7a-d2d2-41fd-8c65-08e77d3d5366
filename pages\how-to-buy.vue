<script setup lang="ts">
const { t } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('how.title'))
})

const list = ref([
	{
		title: t('how.title-1'),
		text: t('how.text-1'),
		icon: 'ui:search',
	},

	{
		title: t('how.title-2'),
		text: t('how.text-2'),
		icon: 'ui:quantity',
	},

	{
		title: t('how.title-3'),
		text: t('how.text-3'),
		icon: 'ui:cart',
	},

	{
		title: t('how.title-4'),
		text: t('how.text-4'),
		icon: 'ui:pin',
	},

	{
		title: t('how.title-5'),
		text: t('how.text-5'),
		icon: 'ui:paymnet-method',
	},
])

useSeoMeta({
	title: () => `${t('how.meta-title')}`,
	description: () => t('how.meta-description'),
	ogTitle: () => `${t('how.meta-title')}`,
	ogDescription: () => t('how.meta-description'),
	twitterTitle: () => `${t('how.meta-title')}`,
	twitterDescription: () => t('how.meta-description'),
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)

useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="relative text-center justify-center gap-4 text-white  rounded-lg !p-0">
			<NuxtImg
				src="/images/how-to-use/lg.png"
				sizes="1765px"
				width="1765"
				height="537"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden lg:block w-full"
				alt="Large screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/how-to-use/md.png"
				sizes="991px"
				width="991"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden md:block lg:hidden w-full"
				alt="Medium screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/how-to-use/sm.png"
				sizes="398px"
				width="398"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover block md:hidden w-full"
				alt="Small screen image"
				:preload="true"
			/>
			<div class="absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0">
				<h1 class="font-bold text-2xl max-sm:text-sm">
					{{ $t('how.title') }}
				</h1>
				<p class="text-xl font-semibold max-sm:text-xs">
					{{ $t('how.sub-title') }}
				</p>
			</div>
		</CardHeader>
		<CardContent class="flex flex-col gap-6 py-12">
			<div
				v-for="(item, index) in list"
				:key="index"
				class="flex w-full justify-between gap-4 "
				:class="{ 'flex-row-reverse': index%2===0 }"
			>
				<div class="flex flex-col w-1/2 p-4 rounded-lg shadow gap-2 border max-sm:w-full justify-center">
					<div class="flex items-center gap-2">
						<span class="w-7 h-7 bg-primary-500 text-white flex items-center justify-center rounded-full">
							{{ index + 1 }}
						</span>
						<h2 class="text-base font-semibold">
							{{ item.title }}
						</h2>
					</div>
					<span class="text-sm text-gray-600">{{ item.text }}</span>
				</div>

				<div class="flex h-52 w-1/2 bg-sky-50 justify-center items-center rounded-lg shadow max-sm:hidden">
					<Icon
						:name="item.icon"
						size="75px"
					/>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
