<script setup lang="ts">
interface PriceProps {
	modelValue?: string | number
	formatValue?: (value: string | number) => string | number
}
const { modelValue, formatValue } = defineProps<PriceProps>()
const emit = defineEmits<{
	(event: 'update:price', value: number): void
}>()

const priceValue = ref()

/** watch on v-modal **/
watch(() => modelValue, (price) => {
	priceValue.value = Number(price?.value || price)
}, { immediate: true })

/** watch on price-value **/
watch(
	priceValue,
	(price) => {
		const newPrice = formatValue(price?.value || price)
		emit('update:price', newPrice)
		priceValue.value = newPrice
	},
	{ immediate: true, deep: true },
)
</script>

<template>
	<div class="flex w-full gap-1 border border-gray-200 rounded p-2 flex-grow">
		<input
			v-model="priceValue"
			class="no-spinner inline px-1 outline-none w-full"
			type="number"
			name="price"
			v-bind="$attrs"
		>
		<span class="text-xs text-gray-500">{{ $t('JOD') }}</span>
	</div>
</template>
