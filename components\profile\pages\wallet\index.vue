<script setup lang="ts">
import WalletFormModal from './form.vue'
import type { User } from '~/interfaces/auth/auth'
import Paginate from '~/components/ui/pagination/Paginate.vue'
import { useAuthStore } from '~/store/useAuthStore.client'
import type { DataWallet, Wallet } from '~/interfaces/auth/wallet'

const { priceFormat } = useCurrency()
const fetchKey = ref(1)
const authStore = useAuthStore()
const page = ref(1)
const list = ref<DataWallet[]>([])

const { data, error, status } = await useApi<Wallet>('/my/transactions', {
	watch: [fetchKey, page],
	query: {
		perPage: 10,
		page,
		orderBy: 'createdAt,desc',
	},
})

if (error.value) {
	console.log('error on fetching wallet', error.value)
}

watch(() => data, () => {
	list.value = (data.value as Wallet)?.data as DataWallet[]
}, { immediate: true, deep: true })

const loading = computed<boolean>(() => status.value !== 'success' && !data.value)
const isEmptyList = computed<boolean>(() => !list.value?.length)
const isFormModal = ref(false)
const walletBalance = computed(() => (authStore.user as User)?.wallet?.value || 0)
</script>

<template>
	<div class="flex flex-col w-full min-h-full gap-6">
		<Card class="w-full ">
			<CardHeader class="max-sm:hidden">
				<span class="font-bold text-xl">{{ $t('profile.link-wallet-title') }}</span>
			</CardHeader>
			<CardContent>
				<template v-if="loading">
					<Skeleton class="h-24 w-full" />
				</template>
				<template v-else>
					<div class="flex bg-sky-50 justify-between items-start p-4 rounded-lg">
						<div class="flex flex-col">
							<span class="text-lg font-medium">{{ $t('wallet.total-balance') }}</span>
							<span class="text-lg font-bold">{{ priceFormat(walletBalance) }}</span>
						</div>

						<Button
							@click.prevent="isFormModal=true"
						>
							{{ $t('wallet.add-balance') }}
						</Button>
					</div>
				</template>
			</cardcontent>
		</Card>
		<Card class="w-full min-h-96 gap-6 flex flex-col flex-1 flex-grow">
			<CardHeader class="max-sm:hidden">
				<span class="font-bold text-xl">{{ $t('wallet.history') }}</span>
			</CardHeader>
			<CardContent class="flex flex-col flex-1 flex-grow">
				<template v-if="loading">
					<div class="flex flex-col w-full gap-4">
						<Skeleton class="h-8 w-full" />
						<Skeleton class="h-8 w-full" />
						<Skeleton class="h-8 w-full" />
						<Skeleton class="h-8 w-full" />
					</div>
				</template>
				<template v-else-if="isEmptyList">
					<div class="flex flex-col w-full h-full items-center gap-6">
						<Icon
							name="ui:empty-wallet"
							class="w-full h-60 mt-20"
						/>
						<div class="flex text-lg max-w-sm text-center font-semibold">
							{{ $t('wallet.empty-text') }}
						</div>
					</div>
				</template>
				<template v-else>
					<table class="min-w-full divide-y divide-gray-200 bg-white text-sm text-gray-700 border rounded-lg">
						<thead class="p-2">
							<tr>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.identifier') }}
								</th>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.datetime') }}
								</th>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.price') }}
								</th>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.description') }}
								</th>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.balance-after') }}
								</th>
								<th class="px-1 py-3 text-start font-medium">
									{{ $t('wallet.status') }}
								</th>
							</tr>
						</thead>
						<tbody class="divide-y divide-gray-100">
							<tr
								v-for="(item, index) in list"
								:key="index"
								class="hover:bg-gray-50 transition"
								:class="{ 'bg-gray-100': index%2===0 }"
							>
								<td class="px-1 py-3">
									{{ item.userId }}
								</td>
								<td class="px-1 py-3">
									{{ useDateFormat(item?.createdAt, 'DD-MM-YYYY hh:mm A') }}
								</td>
								<td class="px-1 py-3 font-semibold text-start">
									{{ priceFormat(item.amount) }}
								</td>
								<td class="px-1 py-3">
									{{ item.description?.join(' ') || '-' }}
								</td>
								<td class="px-1 py-3 font-semibold">
									{{ priceFormat(item.closingBalance) }}
								</td>
								<td class="px-1 py-3">
									<StatusWallet
										:status="item.status"
										size="sm"
									/>
								</td>
							</tr>
						</tbody>
					</table>
				</template>
			</CardContent>
			<CardFooter>
				<div
					v-if="!loading && !isEmptyList"
					class="flex justify-center items-center w-full"
				>
					<Paginate
						:items-per-page="data?.['per_page']"
						:total="data?.['last_page']"
						:sibling-count="1"
						:show-edges="true"
						:default-page="data?.['current_page']"
						@update:page="(p) => page = p"
					/>
				</div>
			</CardFooter>
		</Card>

		<WalletFormModal
			v-if="isFormModal"
			:user="authStore.user"
			@close:modal="isFormModal = false"
		/>
	</div>
</template>
