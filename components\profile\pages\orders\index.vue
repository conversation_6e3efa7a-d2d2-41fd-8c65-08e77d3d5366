<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import ActiveList from './active-list.vue'
import HistoryList from './history-list.vue'
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Order, OrderResponse, Pagination } from '~/interfaces/auth/order'

const page = ref(1)
const selectedId = ref(null)
const tab = ref('active')

// const statusFilter = computed(() => {
// 	if (tab.value === 'active') {
// 		return ['processing']
// 	}
//
// 	return ['draft', 'received', 'processing', 'canceled', 'completed', 'editing', 'failed', 'refunded', 'forRefund']
// })

const { data, error, status } = await useApi<Order>('/my/orders', {
	query: {
		page,
		perPage: 5,
	},
	watch: [page],
})

const router = useRouter()
const route = useRoute()

const orders = computed<Order[]>(() => (data.value as OrderResponse)?.items as Order[])
const loading = computed<boolean>(() => status.value !== 'success')
const pagination = computed<Pagination>(() => (data.value as OrderResponse)?.pagination as Pagination)

if (error.value) {
	console.log('error on fetch orders', error.value)
}

/**
 * Get an Active tab Component by tab name
 */
const activeTab = computed<Component>(() => {
	switch (tab.value) {
		case 'history':
			return HistoryList

		case 'active':
		default:
			return ActiveList
	}
})

/** Get selected order details */
const selectedOrder = computed<Order>(() => orders.value.find(o => o.orderId === selectedId.value))

/**
 * Set active page tab and order id if it is available
 * @param tab
 * @param orderId
 */
const setPageRoute = (tab: string = 'active', orderId: number = null): void => {
	const query = { ...route.query, tab, orderId }

	if (!orderId) {
		delete query.orderId
	}

	page.value = 1
	router.push({
		name: route.name,
		query,
	})
}

/** watch on route to get the active tab and order id **/
watch(() => route.query, (query) => {
	tab.value = query?.tab as string ?? 'active'
	selectedId.value = Number(query?.orderId) as number || null
}, { immediate: true, deep: true })

const isDesktop = useMediaQuery('(min-width: 600px)')
const hideHeader = computed(() => ['tracking', 'details'].includes(tab.value))
</script>

<template>
	<div class="relative w-full flex-col min-h-full">
		<Card
			v-if="!hideHeader && isDesktop"
			class="mb-6"
		>
			<CardHeader>
				<span class="font-bold text-xl">{{ $t('profile.link-orders-title') }}</span>
			</CardHeader>
		</Card>

		<Card class="w-full min-h-100 border-none shadow-none flex flex-col flex-1 flex-grow">
			<CardHeader
				v-if="!hideHeader"
				class="flex-row gap-2 border-b border-gray-200 pb-0 px-0 sm:text-lg xs:text-base font-bold"
			>
				<button
					class="px-6 border-b pb-4 -bottom-px relative text-gray-400 max-sm:w-1/2"
					:class="{ 'text-primary-600 border-primary-600': tab === 'active' }"
					@click="setPageRoute('active')"
				>
					{{ $t('orders.active-orders') }}
				</button>
				<button
					class="px-6 border-b pb-4 -bottom-px relative text-gray-400 max-sm:w-1/2"
					:class="{ 'text-primary-600 border-primary-600': tab === 'history' }"
					@click="setPageRoute('history')"
				>
					{{ $t('orders.history') }}
				</button>
			</CardHeader>

			<template v-if="loading">
				<CardContent>
					<div class="flex flex-col gap-8 w-full p-4">
						<div
							v-for="(_, index) in Array(3)"
							:key="`loading-${index}`"
							class="flex flex-col gap-4 "
						>
							<Skeleton class="w-full h-12" />
							<div class="flex gap-4 justify-between items-center w-full">
								<Skeleton class="w-1/3 h-8" />
								<Skeleton class="w-1/3 h-8" />
								<Skeleton class="w-1/3 h-8" />
							</div>
							<div class="flex gap-4 justify-between items-center w-full">
								<Skeleton class="w-1/3 h-8" />
								<Skeleton class="w-1/3 h-8" />
								<Skeleton class="w-1/3 h-8" />
							</div>
						</div>
					</div>
				</CardContent>
			</template>
			<template v-else>
				<component
					:is="activeTab"
					:orders="orders"
					:order="selectedOrder"
					:cache-key="activeTab"
					@set:page="setPageRoute"
				/>
			</template>

			<CardFooter v-if="!loading && orders?.length">
				<div
					class="flex justify-center items-center w-full"
				>
					<Paginate
						:items-per-page="pagination?.perPage"
						:total="pagination?.lastPage"
						:sibling-count="1"
						:show-edges="true"
						:default-page="pagination.page"
						@update:page="(p) => page = p"
					/>
				</div>
			</CardFooter>
		</Card>
	</div>
</template>
