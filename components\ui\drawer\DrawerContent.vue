<script lang="ts" setup>
import type { DialogContentEmits, DialogContentProps } from 'reka-ui'
import type { HtmlHTMLAttributes } from 'vue'
import { useForwardPropsEmits } from 'reka-ui'
import { DrawerContent, DrawerPortal } from 'vaul-vue'
import DrawerOverlay from './DrawerOverlay.vue'
import { cn } from '@/lib/utils'

const props = defineProps<DialogContentProps & { class?: HtmlHTMLAttributes['class'], hasHand?: boolean }>()
const emits = defineEmits<{
	close: () => void
} & DialogContentEmits>()

const forwarded = useForwardPropsEmits(props, emits)
defineOptions({
	inheritAttrs: false,
})
</script>

<template>
	<DrawerPortal>
		<DrawerOverlay />
		<DrawerContent
			aria-describedby="Drawer content"
			aria-description="Drawer description"
			v-bind="forwarded"
			:class="cn(
				'fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background',
				props.class,
			)"
			@close="emits('close')"
		>
			<div
				v-if="props.hasHand"
				class="mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted max-h-dvh"
			/>
			<slot />
		</DrawerContent>
	</DrawerPortal>
</template>
