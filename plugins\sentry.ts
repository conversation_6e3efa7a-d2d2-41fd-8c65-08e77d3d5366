// interface Error {
// 	message?: string
// 	stack?: string | object | unknown
// }
import * as Sentry from '@sentry/nuxt'

export default defineNuxtPlugin((nuxtApp) => {
	// Configure Sentry to filter out browser compatibility errors
	Sentry.addEventProcessor((event) => {
		// Filter out the Array.prototype.at compatibility error
		if (event.exception?.values?.[0]?.value?.includes('_sessionEntries.at is not a function')) {
			return null // Don't send this error to <PERSON>try
		}

		// Filter out other similar browser compatibility errors
		if (event.exception?.values?.[0]?.value?.includes('.at is not a function')) {
			return null
		}

		return event
	})

	// nuxtApp.hook('page:start', () => {
	// 	console.log('page:start')
	// 	console.log('NODE_ENV', process.env.NODE_ENV)
	// })
	// Prevents Sentry from logging
	nuxtApp.hook('vue:error', (error: Error, payload: unknown, cycle: unknown) => {
		// Safely log error details without circular references
		console.log('App Error :: ', {
			message: error?.message,
			stack: error?.stack,
			hasPayload: !!payload,
			hasCycle: !!cycle,
			payloadType: payload ? typeof payload : 'undefined',
			cycleType: cycle ? typeof cycle : 'undefined',
		})

		// Safely capture the exception with additional context
		Sentry.withScope((scope) => {
			// Add safe context without circular references
			if (payload) {
				try {
					scope.setContext('payload', JSON.parse(JSON.stringify(payload)))
				} catch {
					scope.setContext('payload', { message: 'Unable to serialize payload due to circular references' })
				}
			}

			if (cycle) {
				scope.setContext('cycle', { message: 'Cycle detected in error handling' })
			}

			// Capture the actual error object, fallback to a generic error if undefined
			Sentry.captureException(error || new Error('Unknown vue:error occurred'))
		})
	})
	// Inside the plugin, after initializing sentry
})
