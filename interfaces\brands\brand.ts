export interface BrandList {
	pagination: Pagination
	items: Brand[]
}

export interface Brand {
	isPublished: boolean
	media: Media
	metaDescription: string
	createdAt: Date
	oldSlug: string
	brandId: number
	inHomePage: boolean
	metaTitle: string
	name: string
	slug: string
	updatedAt: Date
}

export interface Media {
	logoName: Logo
	logo: Logo
}

export interface Logo {
	preview: string
	disk: Disk
	src: string
	fileSize: number
	id: number
	mimeType: MIMEType
}

export enum Disk {
	S3 = 's3',
}

export enum MIMEType {
	ImageJPEG = 'image/jpeg',
	ImagePNG = 'image/png',
}

export interface Pagination {
	total: number
	perPage: number
	lastPage: number
	count: number
	page: number
}
