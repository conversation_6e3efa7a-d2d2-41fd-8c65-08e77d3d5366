<script setup lang="ts">
import { useMediaQuery, useWindowSize } from '@vueuse/core'
import { useInfiniteScroll } from '~/composables/useInfiniteScroll'
import type { BreadcrumbsLink } from '~/interfaces/breadcrumbs'
import type { ProductList, Item, Pagination, Filters as CategoryFilters } from '~/interfaces/product/product-list'
import type { CategoryDetails } from '~/interfaces/category/details'
import Filters from '~/components/filter/index.vue'

const { height: screenHeight } = useWindowSize()

const { category, breadcrumbs, isSearchPage = false, showPrevBtn } = defineProps<{
	category?: CategoryDetails | null
	breadcrumbs?: BreadcrumbsLink[]
	isSearchPage?: boolean
	showPrevBtn?: boolean
}>()

// useRouteCache((helper) => {
// 	helper.setMaxAge(60).setCacheable().addTags(['search'])
// })

// useCDNHeaders((helper) => {
// 	helper
// 		.addTags(['search'])
// 		.public()
// 		.setNumeric('maxAge', 60) // 1min
// 		.setNumeric('staleIfError', 43200)
// })

const PER_PAGE = 24
const router = useRouter()
const route = useRoute()
const products = ref<Item[]>([])
const loadingMore = ref<boolean>(false)
const loadMoreBtnRef = ref<HTMLButtonElement | null>(null)
const isDesktop = useMediaQuery('(min-width: 600px)', { ssrWidth: 1000 })
const { locale } = useI18n()

/** Is search page **/
const localePath = useLocalePath()

const slug = computed(() => String(route.params.slug || ''))
const slugs = computed<string[]>(() => slug.value.split(','))
const activeSlug = computed(() => {
	if (Array.isArray(slugs.value)) {
		return [...slugs.value].pop()
	}

	return null
})
type QueryType = {
	page?: string
}
const query = computed<QueryType>(() => route.query)
if (query.value?.page && Number(query.value?.page) <= 1) {
	navigateTo(route.path, { redirectCode: 301 })
}
/** Selected filters **/
const selectedFilters = ref<QueryType>(query.value)
const page = computed<number>(() => Number(query.value?.page || 1))

const apiQuery = computed(() => {
	// in case, there are slugs and categories
	let queryText = null
	if (!isSearchPage) {
		queryText = {
			'categories': activeSlug.value,
			'slug': activeSlug.value,
			'slugs[]': slugs.value,
		}
	} else {
		queryText = {
			q: activeSlug.value,
		}
	}
	return {
		...queryText,
		perPage: PER_PAGE,
		page: query.value?.['page'] || 1,
		...selectedFilters.value,
	}
})
/** fetch product api **/
const { data, error, status } = await useApi<ProductList>(`/search`, {
	query: apiQuery,
	watch: [apiQuery, selectedFilters],
})

/** In case of any error **/
if (error.value) {
	if (error.value.statusCode === 404) {
		throw createError({
			statusCode: 404,
		})
	}
	console.error(`Error fetching category:${slug.value} products:`, error.value)
}

const result = computed(() => data.value as ProductList)
const pagination = computed(() => (data.value as ProductList)?.pagination as Pagination)

if (!import.meta.client) {
	products.value = (result.value as ProductList)?.items as Item[]
}
/** Watching on the data result **/
watch(data, () => {
	if (!result.value?.items || !import.meta.client) {
		return
	}

	if (!page.value || page.value == 1) {
		products.value = result.value.items as Item[] || []
	} else {
		products.value = [...(products.value || []), ...(result.value.items || [])] as Item[]
	}
	loadingMore.value = false
}, { immediate: true })

watch(query, (dataQuery) => {
	selectedFilters.value = dataQuery || selectedFilters.value
}, { immediate: true, deep: true })

/** Loading enabled only on the open page **/
const loading = computed<boolean>(() => status.value === 'pending')

/** Disable Load More Btn **/
const disableLoadMore = computed<boolean>(() => {
	if (isEmptyState.value) {
		return true
	}
	return pagination.value?.lastPage <= Number(page.value)
})

/** Show Empty State **/
const isEmptyState = computed<boolean>(() => {
	if (loading.value) {
		return false
	}

	return !products.value?.length
})

/** Next page link */
const nextPagePath = computed(() => {
	const query = new URLSearchParams({
		...route.query,
		page: String(Number(route.query?.page ?? 1) + 1),
	}).toString()

	return `${route.path}?${query}`
})

const title = computed(() => category?.name ?? '')
const suggestion = computed(() => category?.children ?? [])
const totalProducts = computed<number>(() => result.value?.pagination?.total || 0)
const filter = computed(() => result.value?.filters as CategoryFilters)
const isRtl = computed(() => locale.value === 'ar')
/** Next page link */
const prevPagePath = computed(() => {
	const page = Number(route.query.page ?? 1) - 1
	const query = new URLSearchParams({
		...route.query,
		...(page > 1 ? { page: page.toString() } : {}),
	})

	if (page <= 1) query.delete('page')

	const queryString = query.toString()
	return queryString ? `${route.path}?${queryString}` : route.path
})

const onPrevLink = () => {
	products.value = []
	setTimeout(() => {
		navigateTo(localePath(prevPagePath.value))
	}, 300)
}
/**
 * Update the filter values accumulation
 * @param selected
 */
const onFilterUpdate = (selected: Record<string, string | string[] | null | undefined>) => {
	products.value = []
	status.value = 'pending'

	const query = route.query
	if (!!query.orderBy && selected?.orderBy === '-') {
		query.orderBy = ''
		delete selected?.orderBy
	}

	if (query.page) {
		query.page = ''
	}

	nextTick(() => {
		const filteredQuery = Object.fromEntries(
			Object.entries({
				...query,
				...selected,
			}).filter(([_, v]) => Boolean(v)),
		)

		router.push({
			path: route.path,
			query: filteredQuery,
		})
	})
}

/** Special loading status for the filters only **/
const pageLoading = computed(() => !!loading.value && !filter.value)

/** Card only loading in case there is loading and no products in a list **/
const cardLoading = computed(() => !!loading.value && !products.value?.length)

/** Get the root margin by 3dvh **/
const rootMargin = computed(() => ((screenHeight.value * 30) / 100) + 'px')

/** load more on infinite scroll */
useInfiniteScroll(loadMoreBtnRef, async () => {
	if (!disableLoadMore.value) {
		loadingMore.value = true
		await navigateTo(nextPagePath.value)
	}
}, rootMargin.value)
</script>

<template>
	<div class="grid grid-cols-4 gap-2 auto-rows-auto">
		<template v-if="!isSearchPage">
			<Card class="col-span-4 max-sm:hidden rounded-t-none border-t-0">
				<Breadcrumb
					:links="breadcrumbs"
					:loading="pageLoading"
				/>
			</Card>
			<div class="col-span-1 max-sm:hidden">
				<Filters
					:filter="filter"
					:loading="pageLoading"
					:update="onFilterUpdate"
				/>
			</div>
		</template>
		<template v-else>
			<div class="flex my-4" />
		</template>
		<div
			class="col-span-3 max-sm:col-span-4"
			:class="{ 'col-span-4': isSearchPage }"
		>
			<Card class="flex flex-col">
				<CardHeader class="flex flex-col max-sm:!px-4">
					<div class="flex w-full justify-between items-center">
						<template v-if="pageLoading && !isSearchPage">
							<Skeleton class="h-7 w-32" />
						</template>
						<template v-else>
							<h1 class="text-md font-bold">
								{{ title }}
							</h1>
						</template>
						<div class="flex">
							<FilterSorting
								v-if="isDesktop"
								:total="totalProducts"
								:update="onFilterUpdate"
								:loading="pageLoading"
							/>
							<FilterMobile
								v-else
								:total="totalProducts"
								:filter="filter"
								:loading="pageLoading"
								:update="onFilterUpdate"
							/>
						</div>
					</div>
					<template v-if="suggestion?.length">
						<CategoriesSuggestion
							:slug="slug"
							:categories="suggestion"
						/>
					</template>
					<div
						v-if="showPrevBtn && page > 1"
						class="flex w-full items-center justify-center py-2"
					>
						<Button
							variant="outline"
							:as-child="true"
						>
							<NuxtLinkLocale
								:to="prevPagePath"
								@click.prevent="onPrevLink"
							>
								<Icon
									name="lucide:arrow-left"
									class="h-4 w-4 "
									:class="{ 'rotate-180': isRtl }"
								/>
								<span>{{ $t('form.prev-page') }}</span>
							</NuxtLinkLocale>
						</Button>
					</div>
				</CardHeader>
				<CardContent class="grid gap-4 pb-6 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xs:px-4">
					<ProductCard
						v-for="(product, index) in products"
						:key="product.productId"
						:product="product"
						:image-loading="index >= 8 ? 'lazy' : 'eager'"
					>
						<template #title="{ product }">
							<h2>{{ product?.name }}</h2>
						</template>
					</ProductCard>

					<template v-if="!!cardLoading">
						<ProductCard
							v-for="(_, index) in Array(PER_PAGE)"
							:key="`product.${_}${index}`"
							:loading="true"
						/>
					</template>

					<template v-if="isEmptyState">
						<div class="col-span-4 flex flex-col gap-4 justify-center items-center py-12">
							<template v-if="!isSearchPage">
								<Icon
									name="ui:empty-categories"
									class="h-80 w-80"
								/>
								<span class="text-lg font-bold">{{ $t('product.empty-list-text') }}</span>
							</template>
							<template v-else>
								<Icon
									name="ui:empty-search"
									class="h-80 w-80"
								/>
								<span class="text-lg font-bold">{{ $t('product.empty-search-text') }}</span>
							</template>
						</div>
					</template>
				</CardContent>
				<CardFooter
					v-if="!disableLoadMore"
					class="flex items-center justify-center p-5"
				>
					<Button
						v-if="products?.length"
						variant="outline"
						:size="'lg'"
						:as-child="true"
						class="[&_svg]:size-10"
					>
						<NuxtLink
							:disabled="loadingMore"
							:to="nextPagePath"
						>
							<template v-if="!loadingMore">
								{{ $t('categories.more-btn-title') }}
							</template>
							<template v-else>
								<Spinner />
							</template>
						</NuxtLink>
						<span />
					</Button>
				</CardFooter>
				<div ref="loadMoreBtnRef" />
			</Card>
		</div>
	</div>
</template>
