export interface Filter {
	search: Search
	price: Price
	brandId: BrandID
	storage: RAM
	category: Category
	ram: RAM
}

export interface BrandID {
	name: string
	options: BrandIDOption[]
	type: BrandIDType
	value: unknown[]
}

export interface BrandIDOption {
	meta: PurpleMeta
	text: string
	value: number
}

export interface PurpleMeta {
	type: BrandIDType
	facet: number
}

export enum BrandIDType {
	BrandID = 'brandId',
}

export interface Category {
	name: string
	options: CategoryOption[]
	type: string
	value: number[]
}

export interface CategoryOption {
	meta: FluffyMeta
	text: string
	value: number
}

export interface FluffyMeta {
	facet: number
}

export interface Price {
	name: string
	type: string
	config: PriceConfig
}

export interface PriceConfig {
	min: number
	max: number
}

export interface RAM {
	name: string
	options: RAMOption[]
	type: RAMType
	config: RAMConfig
}

export interface RAMConfig {
	prefix: string
	suffix: string
}

export interface RAMOption {
	meta: TentacledMeta
	text: string
	value: number
}

export interface TentacledMeta {
	number: string
	text: string
	type: RAMType
	facet: number
}

export enum RAMType {
	Number = 'number',
}

export interface Search {
}
