<script setup lang="ts">
import { toast } from 'vue-sonner'
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { t } = useI18n()
const isSaving = ref<boolean>(false)

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		email: z.string().email(t('error.email')).min(2, t('error.required')),
	})),

	initialValues: {
		email: '',
	},
})

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

const changeEmail = Form.handleSubmit(async () => {
	isSaving.value = true
	return authStore.changeEmail(Form.values.email)
		.then(() => {
			authStore.fetchUser()
			toast.success(t('form.email-changed-success'))
			return nextTick(() => emit('close:modal'))
		})
		.catch((error) => {
			throw error
		})
		.finally(() => {
			nextTick(() => isSaving.value = false)
		})
})
</script>

<template>
	<Modal
		:title="$t('form.change-email-title')"
		:dismissible="true"
		@close="emit('close:modal')"
	>
		<template #body>
			<FormField
				v-slot="{ componentField }"
				name="email"
			>
				<FormItem class="px-4 w-full">
					<FormLabel class="font-bold">
						{{ $t('form.email') }}*
					</FormLabel>
					<FormControl>
						<Input
							type="email"
							:placeholder="$t('form.email')"
							v-bind="componentField"
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			</FormField>
		</template>

		<template #footer>
			<div class="flex w-full mt-2">
				<Button
					class="w-full"
					:loading="isSaving"
					@click="changeEmail"
				>
					{{ $t('form.change-email-title') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
