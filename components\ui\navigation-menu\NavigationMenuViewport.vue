<script setup lang="ts">
import {
	NavigationMenuViewport,
	type NavigationMenuViewportProps,
	useForwardProps,
} from 'reka-ui'
import { computed, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<NavigationMenuViewportProps & { class?: HTMLAttributes['class'], align: 'start' | 'end' }>()

const delegatedProps = computed(() => {
	const { class: _, ...delegated } = props

	return delegated
})

const forwardedProps = useForwardProps(delegatedProps)
</script>

<template>
	<div class="absolute rtl:-end-1/2 ltr:-start-1/2 top-full flex justify-center">
		<NavigationMenuViewport
			v-bind="forwardedProps"
			:class="
				cn(
					'origin-top-center relative mt-1.5 h-[--reka-navigation-menu-viewport-height] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 max-md:w-[--reka-navigation-menu-viewport-width]',
					props.class,
				)
			"
		/>
	</div>
</template>
