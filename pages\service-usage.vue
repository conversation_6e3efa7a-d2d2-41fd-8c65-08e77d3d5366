<script setup lang="ts">
import type { Page } from '~/interfaces/pages'

const { t } = useI18n()
const { data } = await useApi<Page>('pages/terms-of-service')
const body = computed(() => (data.value as Page)?.body)
const title = computed(() => (data.value as Page)?.name)
const description = computed(() => (data.value as Page)?.metaDescription)

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(title.value)
})

useSeoMeta({
	title: () => `${t('service-usage.meta-title')}`,
	description: () => t('service-usage.meta-description'),
	ogTitle: () => `${t('service-usage.meta-title')}`,
	ogDescription: () => t('service-usage.meta-description'),
	twitterTitle: () => `${t('service-usage.meta-title')}`,
	twitterDescription: () => t('service-usage.meta-description'),
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="flex flex-col w-full h-full my-6">
		<CardHeader class="text-center justify-center gap-4 py-12">
			<h1 class="font-bold text-2xl">
				{{ title }}
			</h1>
			<h2 class="text-lg text-gray-600">
				{{ description }}
			</h2>
		</CardHeader>

		<CardContent class="flex flex-col gap-6">
			<div class="flex flex-col gap-4 bg-sky-50 p-4 rounded">
				<div class="flex items-center gap-2">
					<div class="flex rounded-full p-2 bg-sky-100">
						<Icon name="ui:privacy" />
					</div>
				</div>
				<div
					class="text-sm text-gray-600 ps-6"
					v-html="body"
				/>
			</div>
		</CardContent>
	</Card>
</template>
