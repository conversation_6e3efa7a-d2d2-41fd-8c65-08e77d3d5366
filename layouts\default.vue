<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import Modal from '~/components/modal.vue'
import type { User } from '~/interfaces/auth/auth'
import { useCategoriesStore } from '~/store/categoriesStore'

import { useAuthStore } from '~/store/useAuthStore.client'
import { useCompareStore } from '~/store/useCompareStore'

const categoryStore = useCategoriesStore()
const compareStore = useCompareStore()
await categoryStore.fetch()
const isDesktop = useMediaQuery('(min-width: 600px)')
const authStore = useAuthStore()
const i18nHead = useLocaleHead()
const route = useRoute()
const router = useRouter()
const localePath = useLocalePath()
const drawer = computed(() => route.query?.drawer as string)

useHead(() => ({
	htmlAttrs: {
		lang: i18nHead.value.htmlAttrs!.lang,
		dir: i18nHead.value.htmlAttrs!.dir,
	},
	link: [...(i18nHead.value.link || [])],
	meta: [...(i18nHead.value.meta || [])],
}))

/** Handle on set wish list drawer */
const closeDrawer = (): void => {
	const query = { ...route.query }
	delete query.drawer
	router.replace({
		path: route.path,
		query,
	})
}

const onCloseCompareMessage = () => {
	compareStore.setConfirmMessage(false)
}

const onCompareList = () => {
	const ids = compareStore.products.join('/')
	router.push(localePath(`/compare/${ids}`))
	onCloseCompareMessage()
}

onBeforeMount(async () => {
	const { data: userData } = await useApi<User>('/my/profile')
	if (userData?.value) {
		authStore.user = userData?.value
	}
})
</script>

<template>
	<div class="layout">
		<AppHeader />

		<div class="container overflow-hidden">
			<slot />
		</div>
		<DelayHydration>
			<LazyAppFooter />
		</DelayHydration>
	</div>
	<ClientOnly>
		<LazyDrawerWishList
			:is-open="drawer === 'wishlist'"
			hydrate-on-idle
			@set:wish-list="closeDrawer"
		/>

		<LazyDrawerCartList
			:is-open="drawer === 'cart'"
			hydrate-on-idle
			@set:cart-list="closeDrawer"
		/>
	</ClientOnly>

	<ClientOnly>
		<LazyAuth hydrate-on-idle />
	</ClientOnly>

	<ClientOnly>
		<LazyToaster
			rich-colors
			:toast-options="{
				duration: 8000,

			}"
			:position="isDesktop?'bottom-right':'top-center'"
			hydrate-on-idle
		/>

		<Modal
			v-if="compareStore.isConfirmMessage"
			:dismissible="false"
			:title="$t('compare.title')"
			@close="onCloseCompareMessage"
		>
			<template #body>
				<div class="flex px-6 pt-2">
					{{ $t('form.compare-message') }}
				</div>
			</template>
			<template #footer>
				<Button
					variant="default"
					class="sm:min-w-24 xs:min-w-full"
					@click="onCompareList"
				>
					{{ $t('form.go-to-compare') }}
				</Button>

				<Button
					variant="outline"
					class="sm:min-w-24 xs:min-w-full"
					@click="onCloseCompareMessage"
				>
					<span>{{ $t('form.cancel') }}</span>
				</Button>
			</template>
		</Modal>
	</ClientOnly>
</template>

<style lang="scss" scoped>
.layout {
  @apply w-full min-h-dvh flex flex-col items-center justify-center bg-body antialiased;
}
</style>
