<script setup lang="ts">
import { useEventListener } from '@vueuse/core'

const route = useRoute()
const router = useRouter()

const threshold = 100
const scrollDirectionThreshold = 20 // Minimum pixels to change scroll direction
const lastScrollY = ref<number>(0)
const previousScrollY = ref<number>(0)
const hasScroll = ref<boolean>(false)
const isScrollingUp = ref<boolean>(false)
const showLinksMenu = computed<boolean>(() => {
	// Show menu when not scrolled or when scrolling up (and past threshold)
	return !hasScroll.value || (hasScroll.value && isScrollingUp.value)
})

/** Event listener with debouncing and hysteresis **/
let ticking = false
useEventListener('scroll', () => {
	if (!ticking) {
		requestAnimationFrame(() => {
			const currentScrollY = window.scrollY
			const scrollDifference = currentScrollY - lastScrollY.value

			// Only update scroll direction if the difference is significant enough
			if (Math.abs(scrollDifference) > scrollDirectionThreshold) {
				isScrollingUp.value = scrollDifference < 0
			}

			// Add hysteresis to prevent rapid toggling
			if (!hasScroll.value && currentScrollY > threshold + 20) {
				hasScroll.value = true
			} else if (hasScroll.value && currentScrollY < threshold - 20) {
				hasScroll.value = false
			}

			previousScrollY.value = lastScrollY.value
			lastScrollY.value = currentScrollY
			ticking = false
		})
		ticking = true
	}
}, {})

/** Handle on set wish list drawer */
const setDrawerPage = (drawer: string): void => {
	router.push({
		path: route.path,
		query: {
			...route.query,
			drawer,
		},
	})
}
</script>

<template>
	<header
		:class="{ '-translate-y-8': hasScroll, 'has-scrolling': hasScroll }"
		class="flex w-full flex-col transition-transform duration-300 ease-out justify-center items-center shadow-lg pb-3 bg-white z-10 sm:pb-0 sticky top-0 max-sm:!pb-0"
	>
		<AppHeaderTop />
		<AppHeaderActions
			:has-scroll="hasScroll"
			@open:drawer="setDrawerPage"
		/>
		<AppHeaderMobile @open:drawer="setDrawerPage" />
		<AppHeaderLinksMenu
			class="menu-list"
			:class="{ 'h-visible': showLinksMenu }"
		/>
	</header>
</template>

<style lang="scss" scoped>
.menu-list {
	will-change: max-height;
	overflow: hidden;
	max-height: 0;
	transition: max-height 300ms cubic-bezier(0.4, 0, 0.2, 1);
	@apply pb-2;
	&.h-visible {
		transition: max-height 300ms cubic-bezier(0.4, 0, 0.2, 1);
		max-height: 48px;
		will-change: max-height;
	}
}

.has-scrolling {
  @apply sm:bg-white/80 backdrop-blur-md;

  // mobile implementations
  @apply max-sm:-translate-y-[100px] pb-0 max-sm:bg-opacity-75;

  :deep(.app-logo) {
		@apply max-h-10 transition-all;
		transition-duration: 300ms;
  }
}
</style>
