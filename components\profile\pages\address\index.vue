<script setup lang="ts">
import { addressDefaultValues } from 'assets/seed/address'
import { toast } from 'vue-sonner'
import AddressCard from './card.vue'
import AddressForm from './form.vue'
import AddressAddedModal from './save-success.vue'
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Address, AddressResponse, Pagination } from '~/interfaces/auth/address'

const { t } = useI18n()
// to re-fetch on demand
const fetchKey = ref(1)
const isNewAddress = ref(false)
const page = ref(1)

const { data, error, status } = await useApi<AddressResponse>('/my/addresses', {
	query: {
		page,
		perPage: 5,
	},
	watch: [fetchKey, page],
})

const addresses = computed(() => (data.value as AddressResponse)?.items || [] as Address[])
const loading = computed(() => status.value !== 'success')
const pagination = computed(() => (data.value as AddressResponse)?.pagination as Pagination)

const isRemoving = ref<boolean>(false)
const deleteAddressId = ref<number | null>(null)
const editAddressId = ref<number | null>(null)
const isNewAddressAdded = ref<boolean>(false)
const addressDetails = computed(() => addresses.value.find(a => a.addressId === deleteAddressId.value || a.addressId === editAddressId.value) ?? { ...addressDefaultValues })

const addressCard = computed(() => {
	const address = addressDetails.value
	if (!address) {
		return
	}

	return [
		address?.city?.country?.name || '',
		address?.city?.name || '',
		address?.district || '',
		address?.street || '',
		address?.apartmentNumber || '',
		address?.buildingNumber || '',
	].join(', ')
})

const isEmptyAddresses = computed(() => !addresses.value?.length as boolean)
if (error.value) {
	console.log('Error on fetch address list:', error.value)
}

const onRemoveAddress = async () => {
	const { $api } = useNuxtApp()

	isRemoving.value = true
	return $api<unknown>(`/my/addresses/${deleteAddressId.value}`, {
		method: 'DELETE',
	}).then(() => {
		deleteAddressId.value = null
		toast.success(t('form.address-deleted-success'))
		fetchKey.value = 1 + fetchKey.value
	}).catch((error) => {
		console.log('Error on remove address:', error)
	}).finally(() => {
		nextTick(() => isRemoving.value = false)
	})
}

/** on close modal **/
const onCloseModal = (): void => {
	editAddressId.value = null
	isNewAddress.value = false
	isNewAddressAdded.value = false
}

/** on fetch address list **/
const onFetchAddressList = (isSaveNewAddress = false): void => {
	fetchKey.value = 1 + fetchKey.value
	onCloseModal()
	isNewAddressAdded.value = isSaveNewAddress
}
</script>

<template>
	<Card class="w-full min-h-full">
		<CardHeader>
			<div class="flex justify-between items-center gap-2">
				<span class="font-bold text-xl max-sm:hidden">{{ $t('profile.link-address-title') }}</span>

				<Button
					v-if="!isEmptyAddresses"
					class="px-6"
					@click.prevent="isNewAddress = true"
				>
					<span>{{ $t('address.add-new-address') }}</span>
				</Button>
			</div>
		</CardHeader>

		<CardContent class="flex-grow">
			<div class="flex flex-col gap-4">
				<template v-if="loading">
					<AddressCard
						v-for="(_, index) in Array(3)"
						:key="`loading-address-${index}`"
						:loading="true"
					/>
				</template>
				<template v-else-if="isEmptyAddresses">
					<div class="flex flex-col gap-4 w-full justify-center items-center">
						<Icon
							name="ui:empty-address"
							class="w-72 h-72"
						/>
						<span class="text-lg font-bold max-w-96 text-center">{{ $t('address.empty-text') }}</span>

						<Button
							class="px-6"
							@click.prevent="() => isNewAddress = true"
						>
							<span>{{ $t('address.add-new-address') }}</span>
						</Button>
					</div>
				</template>

				<template v-else>
					<div class="flex flex-col gap-4 w-full">
						<AddressCard
							v-for="address in addresses"
							:key="address.addressId"
							:address="address"
							@fetch:address="() => { fetchKey = 1+fetchKey }"
							@delete:address="(id) => deleteAddressId = id"
							@edit:address="(id) => editAddressId = id"
						/>
					</div>
				</template>
			</div>
		</CardContent>
		<CardFooter>
			<div
				v-if="!loading && !isEmptyAddresses"
				class="flex justify-center items-center w-full"
			>
				<Paginate
					:items-per-page="pagination?.perPage"
					:total="pagination?.lastPage"
					:sibling-count="1"
					:show-edges="true"
					:default-page="pagination.page"
					@update:page="(p) => page = p"
				/>
			</div>
		</CardFooter>
	</Card>

	<Modal
		v-if="deleteAddressId"
		:title="$t('form.address-delete-title')"
		:description="$t('form.address_delete-text')"
		@close="deleteAddressId = null"
	>
		<template #body>
			<div class="px-4 w-full">
				<div class="flex gap-4 flex-col border rounded-lg p-4 w-full">
					<span class="text-base font-bold">{{ $t('form.location') }}:</span>
					<p class="text-sm">
						{{ addressCard }}
					</p>
				</div>
			</div>
		</template>

		<template #footer>
			<div class="flex justify-end gap-4 w-full">
				<Button
					variant="outline"
					class="px-8"
					@click="deleteAddressId = null"
				>
					{{ $t('form.cancel') }}
				</Button>

				<Button
					variant="danger"
					class="px-10"
					:loading="isRemoving"
					@click="onRemoveAddress"
				>
					{{ $t('form.remove') }}
				</Button>
			</div>
		</template>
	</Modal>
	<AddressForm
		v-if="editAddressId || isNewAddress"
		:address="addressDetails"
		@fetch:address="onFetchAddressList"
		@close:address="onCloseModal"
	/>

	<AddressAddedModal
		v-if="isNewAddressAdded"
		@close:address="onCloseModal"
	/>
</template>

<style scoped lang="scss">

</style>
