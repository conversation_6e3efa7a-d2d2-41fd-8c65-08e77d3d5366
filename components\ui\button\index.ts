import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'

export { default as <PERSON><PERSON> } from './Button.vue'

export const buttonVariants = cva(
	'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0',
	{
		variants: {
			variant: {
				'icon': 'bg-gray-100 text-gray-700 aspect-square hover:text-gray-400 text-gray-500/80 box-shadow',
				'default': 'bg-primary-600 text-white hover:bg-primary-200',
				'destructive':
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
				'outline':
          'border border-primary-600 text-primary-600 hover:bg-primary-200 hover:text-primary-500 active:text-primary-700 disabled:text-opacity-60',
				'outline-secondary':
          'border border-gray-300 text-gray-600 hover:bg-gray-100 hover:text-gray-600 active:text-gray-200 disabled:text-opacity-60',
				'secondary':
          'bg-gray-300 text-gray-600 hover:bg-gray-200',
				'ghost': 'hover:bg-accent hover:text-accent-foreground',
				'link': 'text-primary-100 underline-offset-4 hover:underline',
				'white': 'text-black bg-white  hover:shadow-lg',
				'text': 'text-primary-500 hover:bg-primary-100 cursor-pointer',
				'danger': 'text-white bg-rose-500 cursor-pointer',
			},
			size: {
				default: 'h-10 px-4 py-2',
				sm: 'h-9 rounded-md px-3 text-xs',
				lg: 'h-11 rounded-md px-8',
				icon: 'h-10 w-10',
				circle: 'rounded-full',
			},
		},
		defaultVariants: {
			variant: 'default',
			size: 'default',
		},
	},
)

export type ButtonVariants = VariantProps<typeof buttonVariants>
