export interface AddressResponse {
	items?: Address[]
	pagination?: Pagination
}

export interface Pagination {
	total?: number
	count?: number
	perPage?: number
	page?: number
	lastPage?: number
}

export interface Address {
	addressId: number
	cityId?: number
	district?: string
	createdAt?: string
	updatedAt?: string
	userId?: number
	phone?: Phone
	street?: unknown
	recipientName?: string
	apartmentNumber?: unknown
	buildingNumber?: unknown
	default?: number
	visitorId?: unknown
	firstName?: string
	lastName?: string
	email?: unknown
	buildingType?: string
	fullAddress?: string
	isProfileDataShared?: boolean
	city?: City
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
}

export interface City {
	cityId?: number
	name?: string
	countryId?: number
	codeCity?: string
	createdAt?: string
	updatedAt?: string
	country?: Country
}

export interface Country {
	countryId?: number
	name?: string
	createdAt?: string
	updatedAt?: string
	mediaId?: unknown
	ISOCode?: string
	phoneCode?: string
}

export interface CityResponse {
	text: string
	value: number
}
