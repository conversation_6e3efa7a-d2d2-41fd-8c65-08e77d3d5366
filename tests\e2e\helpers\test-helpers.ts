import { type Page, type Locator, expect } from '@playwright/test'
import { testI18n } from './i18n-helper'

export class AuthHelpers {
	constructor(public readonly page: Page) {}

	// Login modal elements
	get loginModal(): Locator {
		return this.page.locator('[role="dialog"]')// .filter({ has: this.page.locator('img[alt*="action-mobile-logo"]') })
	}

	get phoneInput(): Locator {
		return this.loginModal.locator('.base-phone-input input')
	}

	get passwordInput(): Locator {
		return this.loginModal.locator('input[type="password"]')
	}

	get loginButton(): Locator {
		return this.loginModal.getByRole('button', { name: testI18n.t('auth.log-in') })
	}

	get signUpLink(): Locator {
		return this.loginModal.getByRole('link', { name: testI18n.t('auth.sign-up') })
	}

	get forgotPasswordLink(): Locator {
		return this.loginModal.getByRole('link', { name: testI18n.t('auth.forget-password-question') })
	}

	get closeButton(): Locator {
		return this.loginModal.locator('button[aria-label="Close"]')
	}

	// Helper methods
	async openLoginModal(): Promise<void> {
		await this.page.goto('/?auth=login', { timeout: 60000 })
		await expect(this.loginModal).toBeVisible({ timeout: 30000 })
	}

	async fillLoginForm(phone: string, password: string): Promise<void> {
		// await this.phoneInput.fill(phone)
		// await this.passwordInput.fill(password)
		await this.phoneInput.click()
		await this.phoneInput.clear()
		await this.phoneInput.type(phone)

		await this.passwordInput.click()
		await this.passwordInput.clear()
		await this.passwordInput.type(password)
	}

	async submitLogin(): Promise<void> {
		await this.loginButton.click()
	}

	async login(phone: string, password: string): Promise<void> {
		await this.openLoginModal()
		await this.fillLoginForm(phone, password)
		await this.submitLogin()
	}

	async expectLoginModalVisible(): Promise<void> {
		await expect(this.loginModal).toBeVisible({
			timeout: 10000,
		})
	}

	async expectLoginModalHidden(): Promise<void> {
		await expect(this.loginModal).not.toBeVisible()
	}

	async expectErrorMessage(message?: string): Promise<void> {
		if (message) {
			await expect(this.loginModal.locator('.text-destructive')).toContainText(message)
		} else {
			await expect(this.loginModal.locator('.text-destructive')).toBeVisible()
		}
	}
}

export class AppHelpers {
	constructor(public readonly page: Page) {}

	// Header elements
	get header(): Locator {
		return this.page.locator('header')
	}

	get loginHeaderButton(): Locator {
		return this.header.getByRole('link', { name: /login|sign in/i })
	}

	get userMenuButton(): Locator {
		return this.header.locator('[data-testid="user-menu"]')
	}

	// Common page elements
	get loadingSpinner(): Locator {
		return this.page.locator('.spinner, [data-testid="loading"]')
	}

	// Helper methods
	async waitForPageLoad(): Promise<void> {
		await this.page.waitForLoadState('networkidle', { timeout: 60000 })
		await expect(this.loadingSpinner).not.toBeVisible({ timeout: 10000 }).catch(() => {})
	}

	async expectToastMessage(message: string): Promise<void> {
		await expect(this.page.locator('[data-testid="toast"], .toast')).toContainText(message)
	}
}

// Test data helpers
export const TestData = {
	validCredentials: {
		phone: '+962791234567',
		password: 'TestPassword123',
	},
	invalidCredentials: {
		phone: '+962791234567',
		password: 'wrongpassword',
	},
	invalidPhone: {
		phone: '12345678',
		password: 'TestPassword123',
	},
}
