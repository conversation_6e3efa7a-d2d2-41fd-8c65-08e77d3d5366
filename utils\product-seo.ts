import type { UseHeadInput } from '@unhead/vue'
import type { Details } from '~/interfaces/product/details'
import type { Rate, Review } from '~/interfaces/product/rate'

export const ProductSEO = (product: Details, rate: Rate, reviews: Review[]) => {
	const img = useImage()
	const route = useRoute()
	const { t } = useI18n()
	const config = useRuntimeConfig()
	const localePath = useLocalePath()

	const lastMonth = computed(() => {
		const date = new Date()
		date.setMonth(date.getMonth() + 3)
		return date.toISOString()
	})

	const hasProductGroupSchema = computed(() => route.params?.slug?.length <= 1 && !!product.variances?.length)
	const hasProductSchema = computed(() => route.params?.slug?.length > 1 || !product.variances?.length)

	const productUrl = computed(() => `${config.public.siteUrl}${localePath(route.path)}`)
	const mainPageUrl = computed(() => `${config.public.siteUrl}${localePath('/')}`)
	const hasVariant = computed(() => route.params?.slug?.length > 1)

	const metaTitle = computed(() => {
		if (!hasVariant.value) {
			return `${product?.metaTitle} | ${t('header.meta-site-name')}`
		}

		return `${product?.variance?.metaTitle || ''} ${product?.metaTitle || ''}  | ${t('header.meta-site-name')}`
	})

	const metaDescription = computed(() => {
		if (!hasVariant.value) {
			return `${product?.metaDescription}`
		}

		return `${product?.variance?.metaDescription || ''} ${product?.metaTitle || ''}`
	})

	console.log('product?.media?.cover', product?.media?.cover)
	const productImage = computed(() => {
		return img((product?.media?.gallery?.[0] ?? product?.media?.cover?.[0])?.src, {}, { provider: 'backend' })
	})

	const reviewCount = Number(reviews?.length) || 1
	const reviewList = computed(() => reviews?.map((review) => {
		return ({
			review: {
				'id': review?.userId,
				'@type': 'Review',
				'author': {
					'@type': 'Person',
					'name': `${review.user?.firstName ?? ''} ${review.user?.lastName ?? ''}`,
				},
				'datePublished': new Date()?.toUTCString(),
				'reviewRating': {
					'@type': 'Rating',
					'ratingValue': review?.rating,
					'bestRating': '5',
				},
				'reviewBody': review?.review,
			},
		})
	}))

	const variants = computed(() => {
		return [...product.variances]?.splice(0, 10)?.map(variant => ({
			'@type': 'Product',
			'sku': product?.SKU,
			'image': productImage.value,
			'name': variant?.metaTitle,
			'description': variant?.metaDescription,
			...(variant.stock?.price?.value
				? {
						offers: {
							'@type': 'Offer',
							'url': productUrl.value,
							'priceCurrency': 'JOD',
							'price': `${variant?.stock?.price?.value}`,
							'priceValidUntil': variant?.stock?.unPublishedAt ?? lastMonth.value,
							'itemCondition': 'https://schema.org/NewCondition',
							'availability': 'https://schema.org/InStock',
							'shippingDetails': { '@id': `${mainPageUrl.value}wallet-usage` },
							'hasMerchantReturnPolicy': { '@id': `${mainPageUrl.value}privacy` },
						},
					}
				: {}),

			'aggregateRating': {
				'@type': 'AggregateRating',
				'ratingValue': product?.avgRate,
				'reviewCount': reviewCount,
			},
		}))
	})

	const productGroupSchema = [
		{
			'@context': 'https://schema.org/',
			'@type': 'ProductGroup',
			'name': metaTitle.value,
			'description': metaDescription.value,
			'url': productUrl.value,
			'brand': {
				'@type': 'brand',
				'name': product?.brand?.name,
			},
			'productGroupID': product?.productId,
			'aggregateRating': {
				'@type': 'AggregateRating',
				'ratingValue': product?.avgRate,
				'reviewCount': reviewCount,
			},
			...(product.variances.length ? { hasVariant: variants.value } : {}),
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'OfferShippingDetails',
			'@id': `${mainPageUrl.value}wallet-usage`,
			'shippingRate': {
				'@type': 'MonetaryAmount',
				'value': `${product?.variance?.stock?.price?.value}`,
				'currency': 'JOD',
			},
			'shippingDestination': {
				'@type': 'DefinedRegion',
				'addressCountry': 'JO',
			},
			'deliveryTime': {
				'@type': 'ShippingDeliveryTime',
				'handlingTime': {
					'@type': 'QuantitativeValue',
					'minValue': 0,
					'maxValue': 1,
					'unitCode': 'DAY',
				},
				'transitTime': {
					'@type': 'QuantitativeValue',
					'minValue': 1,
					'maxValue': 5,
					'unitCode': 'DAY',
				},
			},
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'MerchantReturnPolicy',
			'@id': `${mainPageUrl.value}wallet-usage`,
			'applicableCountry': 'JO',
			'returnPolicyCountry': 'JO',
			'merchantReturnDays': 3,
			'returnPolicyCategory': 'https://schema.org/MerchantReturnFiniteReturnWindow',
			'returnMethod': 'https://schema.org/ReturnByMail',
			'returnFees': 'https://schema.org/FreeReturn',
		},
	]
	const productNoVarianceSchema = [
		{
			'@context': 'https://schema.org/',
			'@type': 'Product',
			'name': metaTitle.value,
			'description': metaDescription.value,
			'url': productUrl.value,
			'brand': {
				'@type': 'brand',
				'name': product?.brand?.name,
			},
			'sku': product?.SKU,
			'image': productImage.value,
			...(product?.variance?.stock?.price?.value
				? {
						offers: {
							'@type': 'Offer',
							'url': productUrl.value,
							'priceCurrency': 'JOD',
							'price': `${product?.variance?.stock?.price?.value}`,
							'priceValidUntil': product?.variance?.stock?.unPublishedAt ?? lastMonth.value,
							'itemCondition': 'https://schema.org/NewCondition',
							'availability': 'https://schema.org/InStock',
							'shippingDetails': { '@id': `${mainPageUrl.value}wallet-usage` },
							'hasMerchantReturnPolicy': { '@id': `${mainPageUrl.value}privacy` },
						},
					}
				: {}),
			'aggregateRating': {
				'@type': 'AggregateRating',
				'ratingValue': product?.avgRate,
				'reviewCount': reviewCount,
			},
			...(reviewList.value ? { review: [...reviewList.value] } : {}),
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'OfferShippingDetails',
			'@id': `${mainPageUrl.value}wallet-usage`,
			'shippingRate': {
				'@type': 'MonetaryAmount',
				'value': `${product?.variance?.stock?.price?.value}`,
				'currency': 'JOD',
			},
			'shippingDestination': {
				'@type': 'DefinedRegion',
				'addressCountry': 'JO',
			},
			'deliveryTime': {
				'@type': 'ShippingDeliveryTime',
				'handlingTime': {
					'@type': 'QuantitativeValue',
					'minValue': 0,
					'maxValue': 1,
					'unitCode': 'DAY',
				},
				'transitTime': {
					'@type': 'QuantitativeValue',
					'minValue': 1,
					'maxValue': 5,
					'unitCode': 'DAY',
				},
			},
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'MerchantReturnPolicy',
			'@id': `${mainPageUrl.value}return-policy`,
			'applicableCountry': 'JO',
			'returnPolicyCountry': 'JO',
			'merchantReturnDays': 3,
			'returnPolicyCategory': 'https://schema.org/MerchantReturnFiniteReturnWindow',
			'returnMethod': 'https://schema.org/ReturnByMail',
			'returnFees': 'https://schema.org/FreeReturn',
		},
	]

	const script = [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': productUrl.value,
				'url': productUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionwebsitejo',
					'https://www.instagram.com/actionwebsite/',
					productUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	]

	const __dangerouslyDisableSanitizersByTagID = {
		'ld-json-schema': ['innerHTML'],
	}

	if (hasProductGroupSchema.value) {
		script.push({
			type: 'application/ld+json',
			// @ts-ignore
			id: 'ld-json-product-group',
			innerHTML: JSON.stringify(productGroupSchema),
			tagPosition: 'head',
		})
		__dangerouslyDisableSanitizersByTagID['ld-json-product-group'] = ['innerHTML']
	} else if (hasProductSchema) {
		script.push({
			type: 'application/ld+json',
			// @ts-ignore
			id: 'ld-json-product-no-variance-group',
			innerHTML: JSON.stringify(productNoVarianceSchema),
			tagPosition: 'head',
		})
		__dangerouslyDisableSanitizersByTagID['ld-json-product-no-variance-group'] = ['innerHTML']
	}

	console.log('script', script)

	useHead({
		script,
		__dangerouslyDisableSanitizersByTagID,
	} as UseHeadInput)
}
