<script setup lang="ts">
import { useAuthStore } from '~/store/useAuthStore.client'

const route = useRoute()
const authStore = useAuthStore()
const authPage = ref(null)
const pages = ref({
	'signup': 'AuthSignup',
	'verify-otp': 'AuthVerifyOtp',
	'reset-password': 'AuthResetPassword',
	'forgot-password': 'AuthForgotPassword',
	'login': 'AuthLogin',
})

const isLoggedIn = computed(() => authStore?.isLoggedIn)
const hasPage = computed(() => !isLoggedIn.value && pages.value[authPage.value])
watch(() => route.query, (query) => {
	authPage.value = query?.auth
}, { immediate: true, deep: true })
</script>

<template>
	<div :key="`page-${route.query.auth}`">
		<component
			:is="pages[authPage]"
			v-if="hasPage"
		/>
	</div>
</template>

<style scoped lang="scss">

</style>
