import type { Details } from '~/interfaces/product/details'

export interface CartResponse {
	items?: Items[]
	total?: Total
}

export interface Total {
	value?: number
	currency?: string
	symbol?: string
}

export type Items = Partial<Details> & {
	cartId: number
	varianceId?: number
	productId: number
	bundleId?: unknown
	quantity?: number
	name?: string
	SKU?: string
	type?: string
	variance?: Variance
	media?: Media
}

export interface Variance {
	varianceId?: number
	auctionId?: unknown
	name?: string
	slug?: string
	brandId?: number
	SKU?: unknown
	type?: string
	stock?: Stock
	metaTitle?: string
	metaDescription?: string
	media?: Media
}

export interface Stock {
	stockId?: number
	quantity?: number
	maxPerUser?: number
	supplierId?: number
	isOffer?: boolean
	priceBeforeOffer?: PriceBeforeOffer
	price?: Price
	unPublishedAt?: unknown
	isPreOrder?: boolean
	note?: string
}

export interface PriceBeforeOffer {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Price {
	currencyId?: number
	value?: number
	currency?: string
	symbol?: string
}

export interface Media {
	gallery?: Gallery[]
}

export interface Gallery {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}
