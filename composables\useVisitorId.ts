export const useVisitorId = () => {
	const visitorId = useCookie<string | number>('visitorId', {
		maxAge: 60 * 60 * 24 * 365, // set for 1 year
		sameSite: 'lax',
		path: '/',
		secure: true,
	})

	const generateVisitorId = () => {
		// Generate unique numeric ID using timestamp + random number
		const timestamp = Date.now()
		const random = Math.floor(Math.random() * 10000)
		return `${timestamp}${random}`
	}

	const setVisitorId = () => {
		// make sure that the current visitor id timestamp is more than 27-7-2025 4:00PM GMT+3 (fix release date)
		const visitorIdStr = String(visitorId.value || '')
		const timestampStr = visitorIdStr.substring(0, 13)
		const visitorIdTimestamp = timestampStr && /^\d{13}$/.test(timestampStr) ? Number(timestampStr) : 0

		if (!visitorId.value || visitorIdTimestamp < new Date('2025-07-27T13:00:00Z').getTime()) {
		// Only set if it is not already set
			visitorId.value = generateVisitorId()
		}
	}

	return {
		generateVisitorId,
		setVisitorId,
		visitorId,
	}
}
