<script setup lang="ts">
import { computed } from 'vue'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import type { LockupCategories } from '~/interfaces/lockup/categories'

const localePath = useLocalePath()

const { item } = defineProps<{
	item: LockupCategories
}>()

const children = computed<LockupCategories[] | []>(() => {
	if (item?.children) {
		return item.children
	}

	if (!item?.meta?.children) {
		return []
	}

	return item.meta.children
})
const path = computed(() => localePath('/category'))
const { locale } = useI18n()
const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const isMenu = ref(false)
</script>

<template>
	<DropdownMenu
		v-model:open="isMenu"
		:dir="dir"
	>
		<DropdownMenuTrigger
			v-if="item"
			class="hover:bg-gray-50 p-2 flex items-center text-md cursor-pointer rounded-lg"
		>
			<span class="pe-4 text-nowrap">{{ item.text }} </span>
			<Icon
				name="lucide:chevron-down"
				class="w-3 inline"
			/>
		</DropdownMenuTrigger>
		<DropdownMenuContent
			class="min-w-56"
		>
			<template
				v-for="(link, index) in children"
				:key="`${index}-${JSON.stringify(link.text)}`"
			>
				<AppHeaderLinksSubMenu
					:item="link"
					:close="() => isMenu = false"
					:path="`${path}${item?.meta?.slug?`/${item?.meta?.slug}`:''}`"
				/>
			</template>
		</DropdownMenuContent>
	</DropdownMenu>
</template>
