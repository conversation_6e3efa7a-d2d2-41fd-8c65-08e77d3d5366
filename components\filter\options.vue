<script setup lang="ts">
// @ts-nocheck
import { useDebounceFn, useSorted, useWindowSize } from '@vueuse/core'
import { Checkbox } from '~/components/ui/checkbox'
import type { BrandID, Category, RAM } from '~/interfaces/product/filter'

const { width: screenWidth } = useWindowSize()

const { filter, selected, update } = defineProps<{
	filter: BrandID | Category | RAM | unknown
	selected: string[] | undefined | null
	update?: Function
}>()

const selectedIds = ref({})
const options = useSorted(
	() => filter.options,
	(a, b) => Number(b.meta.facet || 0) - Number(a.meta.facet || 0),
)

/** Selected Items Ids **/
const selectedItemsIds = computed<string>(() => {
	return Object.keys(selectedIds.value).filter(key => selectedIds.value[key]).join(',') as string
})

/** update the filter debouncing */
const debouncedFn = useDebounceFn(() => {
	// make sure the value is not default
	return update(selectedItemsIds.value as string ?? null)
}, screenWidth.value <= 600 ? 10 : 500)

onMounted(() => {
	if (!selected) {
		return
	}

	selected.forEach((id) => {
		selectedIds.value[id] = true
	})
})
</script>

<template>
	<div
		class="flex flex-col w-full gap-3 border-b border-gray-200 pb-4 mt-3"
	>
		<div class="flex w-full">
			<span class="text-sm font-bold">{{ filter?.name }}</span>
		</div>
		<div
			ref="optionRef"
			class="flex h-full flex-col gap-3 pb-2 overflow-y-auto xs:max-h-full sm:max-h-80 pe-1"
		>
			<template
				v-for="(option, index) in options"
				:key="`option-${index}-${filter?.value}`"
			>
				<label
					:for="`option_${filter.name}_${index}`"
					class="flex items-center justify-between cursor-pointer"
				>
					<div
						class="flex items-center gap-2"
					>
						<Checkbox
							:id="`option_${filter.name}_${index}`"
							:model-value="selectedIds[option.value]"
							:value="option.value"
							@update:model-value="(checked) => {
								selectedIds[option.value] = checked ?? undefined;
								debouncedFn()
							}"
						/>
						<span
							class="text-xs text-gray-500 font-medium leading-none"
						>
							{{ option.text }} {{ filter?.config?.suffix }}
						</span>
					</div>

					<div class="flex items-center">
						<span class="text-xs text-gray-500 font-medium leading-none">
							({{ option.meta?.facet }})
						</span>
					</div>
				</label>
			</template>
		</div>
	</div>
</template>
