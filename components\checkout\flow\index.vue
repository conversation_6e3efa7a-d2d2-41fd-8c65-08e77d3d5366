<script setup lang="ts">
import type { CheckoutOrder } from '~/interfaces/checkout/order'

const { order } = defineProps<{
	loading?: boolean
	order?: CheckoutOrder
}>()

const flow = [
	{ component: 'CheckoutFlowAddress' },
	{ component: 'CheckoutFlowShipment' },
	{ component: 'CheckoutFlowPayment' },
	{ component: 'CheckoutFlowFinalize' },
]

const route = useRoute()
const router = useRouter()
const stepId = computed(() => Number(route.params.stepId))
const stepIndex = computed(() => stepId.value - 1)
const activeFlow = computed(() => flow[stepIndex.value])

/** Update checkout step flow **/
const updateRoute = (step: number): void => {
	router.push({
		name: route.name,
		params: {
			orderId: order.orderId,
			stepId: step,
		},
	})
}
</script>

<template>
	<Card>
		<component
			:is="activeFlow?.component"
			:order="order"
			@set:flow="updateRoute"
		/>
	</Card>
</template>
