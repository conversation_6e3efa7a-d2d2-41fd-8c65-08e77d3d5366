<script setup lang="ts">
import type { Details as ProductDetail } from '~/interfaces/product/details'

interface ProductResponse {
	items?: ProductDetail[]
}

const { data, error, status } = await useApi<ProductResponse>('/products', {
	query: {
		perPage: 5,
		categories: 'home-appliances',
		orderBy: 'numberOfOrder,desc',
	},
})

if (error.value) {
	console.error('Error fetching home electronics section:', error.value)
}

const response = computed<ProductResponse>(() => data.value)
const products = computed<ProductDetail[]>(() => response.value?.items)
const isLoading = computed<boolean>(() => status.value !== 'success')
</script>

<template>
	<Card class="col-span-2 flex flex-col max-md:col-span-3">
		<div class="px-4 py-4 justify-between items-center flex">
			<h2 class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.most-popular-electronics') }}
			</h2>

			<NuxtLinkLocale
				to="/category/home-appliances"
				class="max-sm:text-xs text-primary-600"
			>
				{{ $t('home.see-more') }}
			</NuxtLinkLocale>
		</div>
		<CardContent class="grid grid-cols-4 grid-rows-2 gap-4 px-4 max-sm:grid-cols-2 max-sm:grid-rows-3">
			<template v-if="isLoading">
				<div
					v-for="(_, index) in Array(5)"
					:key="`loading-${index}`"
					:class="{ 'row-span-2 col-span-2': index === 0 }"
					class="flex rounded-lg  border-gray-200 border bg-gray-100  justify-center flex-col min-h-52 px-4  max-sm:min-h-36"
				>
					<Skeleton
						class="max-h-48 aspect-video"
					/>
				</div>
			</template>
			<template v-else>
				<NuxtLinkLocale
					v-for="(product, index) in products"
					:key="`product-${index}`"
					:title="product.name"
					:to="`/product/${product.slug}`"
					:class="{ 'row-span-2  col-span-2': index === 0 }"
					class="flex rounded-lg  border-gray-200 border bg-gray-100  justify-center flex-col min-h-52 px-4  max-sm:min-h-36"
				>
					<NuxtImg
						class="w-full"
						:src="product.media?.cover?.[0]?.src"
						provider="backend"
						width="208"
						height="208"
						sizes="208 400"
						format="webp"
						quality="90"
						fit="contain"
						loading="lazy"
						:alt="product?.name"
						:title="product?.name"
					/>
				</NuxtLinkLocale>
			</template>
		</CardContent>
	</Card>
</template>
