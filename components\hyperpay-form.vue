<script setup lang="ts">
import type { PaymentMethodResponse } from '~/interfaces/auth/payment-method'

const { params, callBackUrl = 'my/wallet' } = defineProps<{
	params: PaymentMethodResponse
	callBackUrl?: string
}>()

const { locale } = useI18n()
const formParams = computed(() => params?.params || params.formSign)
const checkoutId = computed(() => formParams.value?.checkoutId)
const formAction = computed(() => {
	return [
		window.location.origin,
		locale.value,
		callBackUrl,
	].join('/')
})

const domain = computed(() => {
	return process.env.NODE_ENV === 'development' ? 'test.oppwa.com' : 'oppwa.com'
})

useHead({
	script: [
		{
			innerHTML: `var wpwlOptions = {
          paymentTarget: "_top",
          style:"card",
          locale: "${locale.value}",
          iframeStyles: {
            'card-number-placeholder': {
              'direction': '"${locale.value === 'ar' ? 'rtl' : 'ltr'}"',
              'text-align': 'center'
            },
            'cvv-placeholder': {
              'direction': 'ltr',
              'text-align': 'center'
            }
          },
          onReady: function () {}
        };`,
			tagPosition: 'bodyClose',
			type: 'text/javascript',
			body: false,
		},
	],
})

watch(checkoutId, async (id) => {
	if (id) {
		useScript({
			src: `https://${domain.value}/v1/paymentWidgets.js?checkoutId=${id}`,
			crossorigin: 'anonymous',
		})
	}
}, { immediate: true, deep: true })
</script>

<template>
	<div
		class="flex relative flex-col w-full gap-2"
		dir="ltr"
	>
		<form
			method="GET"
			class="paymentWidgets"
			data-brands="VISA MASTER"
			:action="formAction"
		>
			<input
				v-for="(value, name) in formParams"
				:key="name"
				type="hidden"
				:name="name"
				:value="value"
			>

			<input
				id="signature"
				type="hidden"
				name="signature"
				value="sign($formParams)"
			>
			<Button
				class="w-full"
				:as-child="true"
			>
				<input
					id="submit"
					type="submit"
					value="Confirm"
				>
			</Button>
			<input
				id="submit"
				class="btn btn-purple btn-block"
				type="submit"
				value=""
			>
		</form>
	</div>
</template>
