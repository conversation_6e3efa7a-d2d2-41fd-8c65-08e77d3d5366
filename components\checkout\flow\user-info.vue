<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import * as z from 'zod'

const { t } = useI18n()

interface User {
	email?: string
	firstName?: string
	lastName?: string
}
const { user } = defineProps<{
	user: User
}>()

const emit = defineEmits<{
	(event: 'close'): void
	(event: 'update', data: object): void
}>()

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		email: z.string().email(t('error.email')),
		firstName: z.string().min(1, t('error.requited')),
		lastName: z.string().min(1, t('error.requited')),
	})),

	initialValues: {
		email: user?.email ?? '',
		firstName: user?.firstName ?? '',
		lastName: user?.lastName ?? '',
	},
})

const onSubmit = Form.handleSubmit((values) => {
	emit('update', values)
	emit('close')
})
</script>

<template>
	<Modal
		:dismissible="false"
		hide-close
		:title="$t('form.user-info')"
		@close="emit('close')"
	>
		<template #body>
			<div class="flex flex-col px-4 gap-4">
				<FormField
					v-slot="{ componentField }"
					name="email"
				>
					<FormItem class=" w-full">
						<FormLabel class="font-bold">
							{{ $t('form.email') }}*
						</FormLabel>
						<FormControl>
							<Input
								type="email"
								:placeholder="$t('form.email')"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>
				<div class="flex w-full items-center gap-2">
					<FormField
						v-slot="{ componentField }"
						name="firstName"
					>
						<FormItem class="w-full">
							<FormLabel class="font-bold">
								{{ $t('form.first-name') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="text"
									:placeholder="$t('form.first-name')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>
					<FormField
						v-slot="{ componentField }"
						name="lastName"
					>
						<FormItem class="w-full">
							<FormLabel class="font-bold">
								{{ $t('form.last-name') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="text"
									:placeholder="$t('form.last-name')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>
				</div>
				<Button @click="onSubmit">
					{{ $t('form.save') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
