export interface PaymentMethodResponse {
	hasFillForm?: boolean
	firstName?: string
	lastName?: string
	email?: string
	city?: string
	address?: string
	country?: unknown
	phone?: string
	hasRedirection?: boolean
	isPaid?: boolean
	status?: unknown
	params?: Params
	formSign?: Params
	continue?: boolean
	amount?: number
	transactionId?: number
	success?: boolean
	wallet?: number
	userId?: number
	sourceId?: number
	source?: string
	walletCashback?: number
	order?: unknown
	paymentMethodId?: unknown
	user?: User
	minutes?: number
}

export interface Params {
	entityId?: string
	amount?: string
	currency?: string
	paymentType?: string
	merchantTransactionId?: number
	customer?: Customer
	billing?: Billing
	checkoutId?: string
}

export interface Customer {
	email?: string
	mobile?: string
	givenName?: string
	surname?: string
}

export interface Billing {
	street1?: string
	city?: string
	country?: string
	state?: string
	postcode?: string
}

export interface User {
	userId?: number
	firstName?: string
	lastName?: string
	email?: string
	status?: string
	emailVerifiedAt?: unknown
	birthday?: unknown
	two_factor_recovery_codes?: unknown
	two_factor_confirmed_at?: unknown
	deleted_at?: unknown
	createdAt?: string
	updatedAt?: string
	phone?: Phone
	gender?: string
	wallet?: number
	phoneVerifiedAt?: string
	numberOfOrders?: number
	fullName?: string
	addresses?: Addresses[]
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
}

export interface Addresses {
	addressId?: number
	cityId?: number
	district?: string
	createdAt?: string
	updatedAt?: string
	userId?: number
	phone?: Phone
	street?: unknown
	recipientName?: string
	apartmentNumber?: unknown
	buildingNumber?: unknown
	default?: number
	visitorId?: unknown
	firstName?: string
	lastName?: string
	email?: unknown
	buildingType?: string
	fullAddress?: string
	city?: City
}

export interface City {
	cityId?: number
	name?: string
	countryId?: number
	codeCity?: string
	createdAt?: string
	updatedAt?: string
}
