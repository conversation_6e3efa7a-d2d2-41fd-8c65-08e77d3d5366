import { cva, type VariantProps } from 'class-variance-authority'

export { default as Badge } from './Badge.vue'

export const badgeVariants = cva(
	'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
	{
		variants: {
			variant: {
				default:
					'border-transparent px-6 font-bold',
				secondary:
					'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
				destructive:
					'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
				outline: 'text-foreground',
				selected: 'bg-[#F3F9FC] rounded-2xl text-gray-600 border-none px-3 shadow-sm',
			},
		},
		defaultVariants: {
			variant: 'default',
		},
	},
)

export type BadgeVariants = VariantProps<typeof badgeVariants>
