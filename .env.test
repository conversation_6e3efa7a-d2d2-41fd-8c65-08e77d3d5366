# Playwright Test Environment Configuration

# Base URL for tests (will be overridden by playwright.config.ts webServer when running locally)
PLAYWRIGHT_BASE_URL=http://localhost:3000

# Test user credentials (replace with actual test user data)
# These should be test accounts in your test environment
TEST_USER_PHONE=+************
TEST_USER_PASSWORD=TestPassword123

# API endpoints for testing
TEST_API_BASE_URL=http://localhost:3000/api

# Test environment flag
NODE_ENV=test
