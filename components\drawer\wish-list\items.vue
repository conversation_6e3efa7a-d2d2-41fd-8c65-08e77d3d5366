<script setup lang="ts">
import type { QuantityPayload } from '~/interfaces/payload'
import type { Wishlist } from '~/interfaces/wishlist/list'
import { useFavoriteStore } from '~/store/useFavoriteStore'
import { useCartStore } from '~/store/useCartStore'

const emit = defineEmits<{
	(event: 'add:wish-list'): void
}>()

const listLength = computed(() => list.value?.length)
const isEmptyList = computed(() => !listLength.value)

const cartStore = useCartStore()
const favoriteStore = useFavoriteStore()
const list = computed(() => (favoriteStore?.list || []) as Wishlist[])
const loading = computed(() => favoriteStore.fetching)
const perPage = computed(() => favoriteStore.perPage)

const onRemoveFromList = async (productId: number) => {
	await favoriteStore.removeFromList(productId)
}

const onAddToCart = async (payload: QuantityPayload) => {
	await cartStore.addToList(payload)
}
</script>

<template>
	<div class="flex flex-col w-full  h-full text-gray-700 overflow-y-auto max-h-full">
		<div class="flex flex-col max-w-full gap-6 p-4">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(perPage)"
					:key="`wish-list-loading-${_}-${index}`"
					class="flex w-full border rounded-lg p-4 gap-4"
				>
					<Skeleton class="flex w-36 h-28" />
					<div class="flex flex-grow p-2 flex-col gap-4">
						<Skeleton class="flex w-full h-8" />
						<Skeleton class="flex w-full h-8" />
					</div>
				</div>
			</template>
			<template v-else-if="isEmptyList">
				<div class="flex flex-col w-full h-full items-center gap-6">
					<Icon
						name="ui:empty-wish-list"
						class="w-full h-60 mt-20"
					/>
					<div class="flex text-lg max-w-xs text-center font-semibold">
						{{ $t('wish-list.empty-text') }}
					</div>

					<Button
						variant="default"
						class="w-1/2"
						@click="() => emit('add:wish-list')"
					>
						{{ $t('wish-list.empty-button') }}
					</Button>
				</div>
			</template>
			<template v-else>
				<DrawerWishListCard
					v-for="item in list"
					:key="`wish-list-item-${item.productId}`"
					:product="item"
					@remove:wish-list="onRemoveFromList"
					@add:cart="onAddToCart"
				/>
			</template>
		</div>
	</div>
</template>
